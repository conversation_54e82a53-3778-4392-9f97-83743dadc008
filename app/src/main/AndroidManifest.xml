<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.rising.high.tech.bigultimatenavdraw">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="com.pax.permission.PRINTER" />

    <uses-feature android:name="android.hardware.camera.any" />

    <uses-permission android:name="com.pax.permission.ICC" />
    <uses-permission android:name="com.pax.permission.PICC" />
    <uses-permission android:name="com.pax.permission.MAGCARD" />
    <uses-permission android:name="com.pax.permission.PRINTER" />
    <uses-permission android:name="com.pax.permission.PED" />

    <application
        android:allowBackup="true"
        android:extractNativeLibs="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".ui.startup.SplashscreenActivity"
            android:label="@string/app_name"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.startup.LoginActivity"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".ui.NaviMainActivity"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateAlwaysHidden"></activity>
    </application>

</manifest>