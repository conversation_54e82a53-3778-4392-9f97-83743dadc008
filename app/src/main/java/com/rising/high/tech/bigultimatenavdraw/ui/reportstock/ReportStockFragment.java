package com.rising.high.tech.bigultimatenavdraw.ui.reportstock;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.res.Resources;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.StockReport;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinCategoryAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.reportstock.adapter.ReportStockProductAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.DbUtil;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;

public class ReportStockFragment extends Fragment {

    private static final String TAG = "ReportStockFragment";
    private Context _context;

    BusinessLocationDbController businessLocationDbController;
    CategoryDbController categoryDbController;
    SpinCategoryAdapter spinCategoryAdapter;
    SpinStationAdapter spinStationAdapter;
    Spinner spinnerCategory, spinnerStation, spinnerUnit;
    private ProgressDialog mProgressDialog;

    RecyclerView report_stock_recycle;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private VariationsDbController variationsDbController;
    private ProductDbController productDbController;
    private ReportStockProductAdapter reportStockProductAdapter;
    private Resources resources;

    TextView stock_price;

    @BindView(R.id.id_filter)
    Button btnFilter;
    @BindView(R.id.noItemFound)
    TextView noItemFound;
    View root;

    public ReportStockFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        root = inflater.inflate(R.layout.fragment_report_stock, container, false);
        ButterKnife.bind(this, root);

        _context = getContext();

        report_stock_recycle = root.findViewById(R.id.report_stock_recycle);

        spinnerCategory = root.findViewById(R.id.spinner_category);
        spinnerStation = root.findViewById(R.id.spinner_station);
        initDB();
        initSpinner();

        report_stock_recycle.setLayoutManager(new LinearLayoutManager(_context));
        Float stock_price_price = 0.f;
     //   stock_price.setText(stock_price_price.toString());

        //get data for report stock
        getReportStock(root, 0, 0);
        return root;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }

    private void showProgress() {
        mProgressDialog = ProgressDialog.show(_context,
                "", "Loading ...", true, false);
    }

    private void initSpinner() {
        spinCategoryAdapter = new SpinCategoryAdapter(_context, android.R.layout.simple_spinner_item, categoryDbController.getAllCategorySpinner());
        spinnerCategory.setAdapter(spinCategoryAdapter);

        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerStation.setAdapter(spinStationAdapter);

        reportStockProductAdapter = new ReportStockProductAdapter();
        report_stock_recycle.setAdapter(reportStockProductAdapter);


        btnFilter.setOnClickListener(v->{
            Business_location business_location=(Business_location) spinnerStation.getSelectedItem();
            Category category=(Category) spinnerCategory.getSelectedItem();

        //    productDbController.getProductCategory(category.getId());

            getReportStock(root, business_location.getId(),category.getId());
        });
    }

    private void initDB() {
        variationLocationDetailDbController = new VariationLocationDetailDbController(_context);

        categoryDbController = new CategoryDbController(_context);

        businessLocationDbController = new BusinessLocationDbController(_context);

        productDbController = new ProductDbController(_context);

        variationsDbController = new VariationsDbController(_context);

    }

    public void setItemView() {
        if (reportStockProductAdapter.getItemCount() > 0) {
            report_stock_recycle.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            report_stock_recycle.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }
    /**
     * Calculate report stock
     */
    private void getReportStock(View view, Integer location_id, Integer category_id) {
        showProgress();
        AsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<Variation_location_details> variation_location_details = variationLocationDetailDbController.getAllVariationLocationDetails(location_id, category_id);
                ArrayList<StockReport> stockReports = new ArrayList<>();

                for (Variation_location_details variation_location_details1 : variation_location_details) {
                    StockReport stockReport = new StockReport();

                    Product product = productDbController.getProductById(variation_location_details1.getProduct_id());
                    stockReport.setProduct(product.getName());

                    Business_location businesslocation = businessLocationDbController.getStationById(variation_location_details1.getLocation_id());
                    stockReport.setLocation(businesslocation.getName());

                    Variation variation = variationsDbController.getVariationByProductId(variation_location_details1.getProduct_id());
                    stockReport.setUnit_price(variation.getSell_price_inc_tax());
                    stockReport.setCurrent_stock(variation_location_details1.getQty_available() + "");

                    Float current_stock_value_bpp = variation_location_details1.getQty_available() * Float.parseFloat(variation.getDpp_inc_tax());
                    stockReport.setCurrent_stock_value_bpp(current_stock_value_bpp + "" );

                    Float current_stock_value_bsp = variation_location_details1.getQty_available() * Float.parseFloat(variation.getSell_price_inc_tax());
                    stockReport.setCurrent_stock_value_bsp(current_stock_value_bsp + "");

                    Float potential_profit = current_stock_value_bsp - current_stock_value_bpp;
                    stockReport.setPotential_profit(potential_profit + "");


                    stockReport.setTotal_unit_sold(DbUtil.getTotalSold(_context, variation_location_details1.getProduct_id(), variation_location_details1.getLocation_id()));
                    stockReport.setTotal_unit_transfered(DbUtil.getTotalTranfered(_context, variation_location_details1.getProduct_id(), variation_location_details1.getLocation_id()));
                    stockReport.setTotal_unit_adjusted(DbUtil.getTotalAdjusted(_context, variation_location_details1.getProduct_id(), variation_location_details1.getLocation_id()));

                    stockReports.add(stockReport);
                }

                view.post(new Runnable() {
                    public void run() {
                        /* the desired UI update */
                        reportStockProductAdapter.setData(stockReports);
                        mProgressDialog.dismiss();
                        setItemView();
                    }
                });
            }
        });
    }

    private void filter() {
        Category category = (Category) spinnerCategory.getSelectedItem();
        Business_location businesslocation = (Business_location) spinnerStation.getSelectedItem();
        Integer unit = spinnerUnit.getSelectedItemPosition();
    }

}