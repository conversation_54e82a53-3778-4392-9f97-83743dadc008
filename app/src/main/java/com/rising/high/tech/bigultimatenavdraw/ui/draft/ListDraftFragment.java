package com.rising.high.tech.bigultimatenavdraw.ui.draft;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.TimePicker;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinContactAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.draft.adapter.DraftAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.draft.adapter.SubDraftAdapter;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DRAFT;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetFullDate;

public class ListDraftFragment extends Fragment {

    private static final String TAG = "ListDraftFragment";
    private Context _context;

    @BindView(R.id.noItemFound)
    TextView noItemFound;

    Spinner spinnerStation;
    Spinner spinnerCustomer;
    Button btnFilter;
    EditText btnStartDate;
    EditText btnEndDate;
    RecyclerView recycle_vente;
    TransactionDbController transactionDbController;
    DraftAdapter draftAdapter;
    private TransactionSellLineDbController transactionSellLineDbController;
    SpinStationAdapter spinStationAdapter;
    BusinessLocationDbController businessLocationDbController;
    final Calendar c = Calendar.getInstance();
    private HashMap<String, String> currentMap = new HashMap<>();


    private SpinContactAdapter spinContactAdapter;
    private ContactDbController contactDbController;

    public ListDraftFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_draft_main, container, false);
        _context = getContext();
        ButterKnife.bind(this, root);

        spinnerStation = root.findViewById(R.id.spinnerStation);
        spinnerCustomer = root.findViewById(R.id.spinner_customer);
        btnFilter = root.findViewById(R.id.id_filter);
        btnStartDate = root.findViewById(R.id.new_start_date);
        btnEndDate = root.findViewById(R.id.new_end_date);

        recycle_vente = root.findViewById(R.id.recycle_vente);
        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();

        draftAdapter = new DraftAdapter();
        recycle_vente.setAdapter(draftAdapter);
        recycle_vente.setLayoutManager(new LinearLayoutManager(_context));


        initDB();
        initListners();
        draftAdapter.setData(transactionDbController.getAllTransactionByStatus(DRAFT));
        draftAdapter.setData(transactionDbController.filterTransaction(DRAFT, 0, 0, null, null, 0));
        setItemView();
        return root;
    }

    private void initListners() {
        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerStation.setAdapter(spinStationAdapter);

        spinContactAdapter = new SpinContactAdapter(_context, android.R.layout.simple_spinner_item, contactDbController.getSpinnerCustomer());
        spinnerCustomer.setAdapter(spinContactAdapter);

        btnStartDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {

                                // select hours and minute
                                TimePickerDialog timepick = new TimePickerDialog(_context, new TimePickerDialog.OnTimeSetListener() {
                                    @Override
                                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                                        // String myFormat =populateSetDate(year,month,day);
                                        String myFormat = populateSetFullDate(year, month, day, hourOfDay, minute);
                                        btnStartDate.setText(myFormat);
                                        currentMap.put("date_debut", myFormat);
//                                        btnStartDate.setBackgroundColor(Color.GRAY);
//                                        btnStartDate.setTextSize(8);
                                        // Time results here
                                    }
                                }, c.get(Calendar.HOUR), c.get(Calendar.MINUTE), true);
                                timepick.setTitle("select time");
                                timepick.show();
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });
        btnEndDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                TimePickerDialog timepick = new TimePickerDialog(_context, new TimePickerDialog.OnTimeSetListener() {
                                    @Override
                                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                                        //String myFormat =populateSetDate(year,month,day);
                                        String myFormat = populateSetFullDate(year, month, day, hourOfDay, minute);
                                        btnEndDate.setText(myFormat);
                                        currentMap.put("date_fin", myFormat);
//                                        btnEndDate.setBackgroundColor(Color.GRAY);
//                                        btnEndDate.setTextSize(8);
                                        // Time results here
                                    }
                                }, c.get(Calendar.HOUR), c.get(Calendar.MINUTE), true);
                                timepick.setTitle("select time");
                                timepick.show();

                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        btnFilter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Business_location businesslocation = (Business_location) spinnerStation.getSelectedItem();
                Contact contact = (Contact) spinnerCustomer.getSelectedItem();

                draftAdapter.setData(transactionDbController.filterTransaction(DRAFT, businesslocation.getId(), contact.getId(), currentMap.get("date_debut"), currentMap.get("date_fin"), 0));
                draftAdapter.notifyDataSetChanged();
                setItemView();
            }
        });

        draftAdapter.setOnDataChangeListener(new DraftAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(Transaction transaction) {
                showDetail(transaction);
            }
        });

    }

    private void initDB() {

        transactionSellLineDbController = new TransactionSellLineDbController(_context);
        transactionSellLineDbController.open();

        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();

        contactDbController = new ContactDbController(_context);
        contactDbController.open();

    }

    private void showDetail(Transaction transaction) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.dialog_draft_vente_detail_main, null);
        SubDraftAdapter subDraftAdapter = new SubDraftAdapter();

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                _context);

        AppCompatImageView btn_close = promptsView.findViewById(R.id.btn_close);
        TextView status = promptsView.findViewById(R.id.status);
        TextView payment_status = promptsView.findViewById(R.id.payment_status);
        TextView customer_name = promptsView.findViewById(R.id.customer_name);
        TextView date = promptsView.findViewById(R.id.date);
        TextView phone = promptsView.findViewById(R.id.phone);

        status.setText(transaction.getStatus());

        payment_status.setText(transaction.getPayment_status());
        Contact contact = contactDbController.getCustomerById(transaction.getContact_id());
        customer_name.setText(contact.getName());
        phone.setText(contact.getMobile());
        date.setText(transaction.getTransaction_date());

        // set prompts.xml to alertdialog builder
        alertDialogBuilder.setView(promptsView);
        ArrayList<Sell_lines> sell_lines = transactionSellLineDbController.getSellLineByTransaction(transaction.getId());
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
        recyclerView.setAdapter(subDraftAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(_context));


        subDraftAdapter.setData(sell_lines);
        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        btn_close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                mAlertDialog.dismiss();
            }
        });
        mAlertDialog.show();
    }


    public void setItemView() {
        if (draftAdapter.getItemCount() > 0) {
            recycle_vente.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recycle_vente.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }


}