package com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact;

import android.app.DatePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.adapter.LedgerVenteAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.sell.adapter.VenteAdapter;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ALL;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class SaleMain extends Fragment {
    private static final String TAG = "SaleMain";
    LedgerVenteAdapter venteAdapter;
    RecyclerView recycle_vente;
    private Context _context;
    TransactionDbController transactionDbController;
    private Integer id_contact;
    final Calendar c = Calendar.getInstance();
    private HashMap<String, String> currentMap = new HashMap<>();

    @BindView(R.id.new_start_date)
    EditText btnStartDate;
    @BindView(R.id.new_end_date)
    EditText btnEndDate;
    @BindView(R.id.id_filter)
    Button btnfilter;
    @BindView(R.id.noItemFound)
    TextView noItemFound;
    @BindView(R.id.spinner_payment_status)
    Spinner spinnerPayementStatus;
    ArrayList<Transaction> transactionArrayList;
    public SaleMain(Integer id) {
        this.id_contact = id;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View PageTwo = inflater.inflate(R.layout.fragment_sale_contact, container, false);
            _context = getContext();
        ButterKnife.bind(this, PageTwo);

        venteAdapter = new LedgerVenteAdapter();
        recycle_vente =  PageTwo.findViewById(R.id.recycle_vente);

        recycle_vente.setAdapter(venteAdapter);
        recycle_vente.setLayoutManager(new LinearLayoutManager(_context));
        return PageTwo;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();

        transactionArrayList=transactionDbController.getTransactionByContactId(id_contact,null, null,ALL);
        venteAdapter.setData(transactionArrayList);
        noItemFound.setVisibility(transactionArrayList.size()==0? View.VISIBLE: View.GONE);
        recycle_vente.setVisibility(transactionArrayList.size()==0? View.GONE: View.VISIBLE);


        btnStartDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                btnStartDate.setText(myFormat);
                                currentMap.put("date_debut", myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();


            }
        });
        btnEndDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                btnEndDate.setText(myFormat);
                                currentMap.put("date_fin", myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();

            }
        });

        btnfilter.setOnClickListener(v->{
            String status = spinnerPayementStatus.getSelectedItem().toString().toLowerCase();
            transactionArrayList=transactionDbController.getTransactionByContactId(id_contact,  currentMap.get("date_debut"), currentMap.get("date_fin"), status);

            venteAdapter.setData(transactionArrayList);

            noItemFound.setVisibility(transactionArrayList.size()==0? View.VISIBLE: View.GONE);
            recycle_vente.setVisibility(transactionArrayList.size()==0? View.GONE: View.VISIBLE);
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void getSumamary() {

    }
}
