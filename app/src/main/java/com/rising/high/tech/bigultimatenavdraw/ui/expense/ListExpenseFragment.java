package com.rising.high.tech.bigultimatenavdraw.ui.expense;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.TimePicker;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinContactAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.expense.adapter.DepenseAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import java.util.Calendar;
import java.util.HashMap;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.EXPENSE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SELL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetFullDate;

public class ListExpenseFragment extends Fragment {
    private static final String TAG = "ListExpenseFragment";

    private Context _context;

    BusinessLocationDbController businessLocationDbController;
    SpinStationAdapter spinStationAdapter;
    SpinContactAdapter spinContactAdapter;
    Spinner spinnerStation;
    RecyclerView recycleVente;
    TransactionDbController transactionDbController;
    ContactDbController contactDbController;
    Button btnAdd;
    DepenseAdapter depenseAdapter;
    final Calendar c = Calendar.getInstance();
    SessionManager session;
    private HashMap<String, String> currentMap = new HashMap<>();

    @BindView(R.id.new_start_date)
    EditText btnStartDate;
    @BindView(R.id.new_end_date)
    EditText btnEndDate;
    @BindView(R.id.filter_header)
    LinearLayout filterHeader;
    @BindView(R.id.filter_container)
    LinearLayout filterContainer;
    @BindView(R.id.spinner_contact)
    Spinner spinnerContact;
    @BindView(R.id.id_filter)
    Button btnFilter;
    @BindView(R.id.spinner_payment_status)
    Spinner spinnerPaymentStatus;

    @BindView(R.id.noItemFound)
    TextView noItemFound;
    public ListExpenseFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.activity_depense, container, false);
        ButterKnife.bind(this, root);

        _context = getContext();
        session = new SessionManager(_context);

        spinnerStation = root.findViewById(R.id.spinnerStation);
        btnAdd = root.findViewById(R.id.id_add);
        recycleVente = root.findViewById(R.id.recycle_vente);

        initDB();
        initSpinner();
        initListners();

        checkRoles();
        return root;
    }
    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER))
        {
            btnAdd.setVisibility(View.INVISIBLE);
        }
    }
    private void initDB(){

        businessLocationDbController = new BusinessLocationDbController(_context);
        transactionDbController = new TransactionDbController(_context);

        contactDbController = new ContactDbController(_context);


    }

    private void initSpinner(){

        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerStation.setAdapter(spinStationAdapter);
        spinContactAdapter = new SpinContactAdapter(_context, android.R.layout.simple_spinner_item, contactDbController.getSpinContacts());
        spinnerContact.setAdapter(spinContactAdapter);

    }
    private void initListners(){
        //init adatpter
        depenseAdapter = new DepenseAdapter();
        recycleVente.setAdapter(depenseAdapter);
        recycleVente.setLayoutManager(new LinearLayoutManager(_context));
        depenseAdapter.setData(transactionDbController.getExpense());
        setItemView();

        filterHeader.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                filterContainer.setVisibility(filterContainer.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE);
            }
        });

        btnStartDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {

                                // select hours and minute
                                TimePickerDialog timepick = new TimePickerDialog(_context, new TimePickerDialog.OnTimeSetListener() {
                                    @Override
                                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                                        // String myFormat =populateSetDate(year,month,day);
                                        String myFormat = populateSetFullDate(year, month, day, hourOfDay, minute);
                                        btnStartDate.setText(myFormat);
                                        currentMap.put("date_debut", myFormat);
//                                        btnStartDate.setBackgroundColor(Color.GRAY);
//                                        btnStartDate.setTextSize(8);
                                        // Time results here
                                    }
                                }, c.get(Calendar.HOUR), c.get(Calendar.MINUTE), true);
                                timepick.setTitle("select time");
                                timepick.show();
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });
        btnEndDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                TimePickerDialog timepick = new TimePickerDialog(_context, new TimePickerDialog.OnTimeSetListener() {
                                    @Override
                                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                                        //String myFormat =populateSetDate(year,month,day);
                                        String myFormat = populateSetFullDate(year, month, day, hourOfDay, minute);
                                        btnEndDate.setText(myFormat);
                                        currentMap.put("date_fin", myFormat);
//                                        btnEndDate.setBackgroundColor(Color.GRAY);
//                                        btnEndDate.setTextSize(8);
                                        // Time results here
                                    }
                                }, c.get(Calendar.HOUR), c.get(Calendar.MINUTE), true);
                                timepick.setTitle("select time");
                                timepick.show();

                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });


        btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new AddExpenseFragment(false));
            }
        });

        depenseAdapter.setOnDataChangeListener(new DepenseAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(Transaction transaction) {
                getResume();
            }
        });

        btnFilter.setOnClickListener(v->{
            Business_location businesslocation = (Business_location) spinnerStation.getSelectedItem();
            String sts = spinnerPaymentStatus.getSelectedItem().toString().toLowerCase();

            depenseAdapter.setData(transactionDbController.filterTransaction(businesslocation.getId(), sts, currentMap.get("date_debut"), currentMap.get("date_fin"), EXPENSE));
            depenseAdapter.notifyDataSetChanged();
            setItemView();
        });
    }
    public void setItemView()
    {
        if(depenseAdapter.getItemCount() > 0)
        {
            recycleVente.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        }
        else
        {
            recycleVente.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }
    private void getResume() {
//        if (depenseAdapter.getItemCount() > 0) {
//            totalContainer.setVisibility(View.VISIBLE);
//
//            Float total_grand = 0.f;
//            int ordered = 0;
//            int pending = 0;
//            int received = 0;
//            int partial = 0;
//            int due = 0;
//            int paid = 0;
//
//            for (Transaction transaction : depenseAdapter.getData()) {
//                total_grand = total_grand + Float.parseFloat(transaction.getFinal_total());
//                switch (transaction.getStatus()) {
//                    case "ordered":
//                        ordered += 1;
//                        break;
//                    case "pending":
//                        pending += +1;
//                        break;
//                    case "received":
//                        received += +1;
//                        break;
//                }
//
//                if (transaction.getPayment_status() != null) {
//                    switch (transaction.getPayment_status()) {
//                        case "partial":
//                            partial += +1;
//                            break;
//                        case "due":
//                            due += +1;
//                            break;
//                        case "paid":
//                            paid += +1;
//                            break;
//                    }
//                }
//            }
//
//
//            containerReceived.setVisibility(received > 0 ? View.VISIBLE : View.GONE);
//            receivedCount.setText(received + "");
//
//
//            containerPending.setVisibility(pending > 0 ? View.VISIBLE : View.GONE);
//            pendingCount.setText(pending + "");
//
//            containerPartial.setVisibility(partial > 0 ? View.VISIBLE : View.GONE);
//            partialCount.setText(partial + "");
//
//            containerDue.setVisibility(due > 0 ? View.VISIBLE : View.GONE);
//            dueCount.setText(due + "");
//
//            containerOrdered.setVisibility(ordered > 0 ? View.VISIBLE : View.GONE);
//            orderedCount.setText(ordered + "");
//
//            containerPaid.setVisibility(paid > 0 ? View.VISIBLE : View.GONE);
//            paidCount.setText(paid + "");
//
//            purchaseGrandTotal.setText(total_grand + "");
//        } else {
//            totalContainer.setVisibility(View.GONE);
//        }
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

}