package com.rising.high.tech.bigultimatenavdraw.ui.role;

import android.app.DatePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PermissionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.RoleDbController;
import com.rising.high.tech.bigultimatenavdraw.db.RoleHasPermissionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UserDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Role;
import com.rising.high.tech.bigultimatenavdraw.model.User;
import com.rising.high.tech.bigultimatenavdraw.model.Warranty;
import com.rising.high.tech.bigultimatenavdraw.ui.usermanagement.UsersFragment;
import com.rising.high.tech.bigultimatenavdraw.util.MultiSelectSpinner;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class AddRoleFragment extends Fragment {

    private static final String TAG = "AddRoleFragment";
    private Context _context;
    final Calendar c = Calendar.getInstance();
    private BusinessLocationDbController businessLocationDbController;
    private UserDbController userDbController;
    private RoleDbController roleDbController;
    private PermissionDbController permissionDbController;
    private RoleHasPermissionDbController roleHasPermissionDbController;

    @BindView(R.id.add_btn)
    Button addBtn;
    @BindView(R.id.id_back)
    Button backBtn;
    @BindView(R.id.name_edittext)
    EditText nameEdittext;

    @BindView(R.id.user_all_chbx)
    CheckBox userAllChbox;
    @BindView(R.id.delete_user_chbx)
    CheckBox deletUserChbox;
    @BindView(R.id.view_user_chbx)
    CheckBox viewUserChbox;
    @BindView(R.id.add_user_chbx)
    CheckBox addUserChbox;
    @BindView(R.id.edit_user_chbx)
    CheckBox editUserChbox;

    @BindView(R.id.all_role_cbox)
    CheckBox allRoleCbox;
    @BindView(R.id.view_role_cbox)
    CheckBox viewRoleCbox;
    @BindView(R.id.add_role_cbox)
    CheckBox addRoleCbox;
    @BindView(R.id.edit_role_cbox)
    CheckBox editRoleCbox;
    @BindView(R.id.delete_role_cbox)
    CheckBox deleteRoleCbox;

    @BindView(R.id.all_supplier_cbox)
    CheckBox allSupplierCbox;
    @BindView(R.id.delete_contact_cbox)
    CheckBox deleteContactCbox;
    @BindView(R.id.add_contact_cbox)
    CheckBox addContactCbox;
    @BindView(R.id.edit_contact_cbox)
    CheckBox editContactCbox;
    @BindView(R.id.view_contact_cbox)
    CheckBox viewContactCbox;

    @BindView(R.id.all_customer_cbox)
    CheckBox allCustomerCbox;
    @BindView(R.id.delete_customer_cbox)
    CheckBox deleteCustomerCbox;
    @BindView(R.id.add_customer_cbox)
    CheckBox addCustomerCbox;
    @BindView(R.id.edit_customer_cbox)
    CheckBox editCustomerCbox;
    @BindView(R.id.view_cutomer_cbox)
    CheckBox viewCustomerCbox;

    @BindView(R.id.all_product_cbox)
    CheckBox allProductCbox;
    @BindView(R.id.delete_product_cbox)
    CheckBox deleteProductCbox;
    @BindView(R.id.add_product_cbox)
    CheckBox addProductCbox;
    @BindView(R.id.edit_product_cbox)
    CheckBox editProductCbox;
    @BindView(R.id.view_product_cbox)
    CheckBox viewProductCbox;
    @BindView(R.id.add_opning_stock_cbox)
    CheckBox addOpningStockCbox;
    @BindView(R.id.view_purchase_price_cbox)
    CheckBox viewPurchasePriceCbox;

    @BindView(R.id.all_purchase_stockadj_cbox)
    CheckBox allPurchaseStockAdjCbox;
    @BindView(R.id.delete_purchase_stockadj_cbox)
    CheckBox deletePurchaseStockAdjCbox;
    @BindView(R.id.add_purchase_stockadj_cbox)
    CheckBox addPurchaseStockAdjCbox;
    @BindView(R.id.edit_purchase_stockadj_cbox)
    CheckBox editPurchaseStockAdjCbox;
    @BindView(R.id.view_purchase_stockadj_cbox)
    CheckBox viewPurchaseStockAdjCbox;
    @BindView(R.id.add_edit_delet_paymemt_cbox)
    CheckBox viewAddEditDeletPaymentAdjCbox;
    @BindView(R.id.view_own_purchase_cbox)
    CheckBox ownPurchaseCbox;

    @BindView(R.id.all_sell_cbox)
    CheckBox allSellCbox;
    @BindView(R.id.delete_pos_sell_cbox)
    CheckBox deletePosSelljCbox;
    @BindView(R.id.add_pos_sell_cbox)
    CheckBox addPosSellCbox;
    @BindView(R.id.edit_pos_sell_cbox)
    CheckBox editPosSelljCbox;
    @BindView(R.id.view_pos_sell_cbox)
    CheckBox viewPosSellCbox;
    @BindView(R.id.list_draft_cbox)
    CheckBox viewListDraftCbox;
    @BindView(R.id.list_quotation_cbox)
    CheckBox listQuotationCbox;
    @BindView(R.id.list_discount_cbox)
    CheckBox listDiscountCbox;
    @BindView(R.id.view_sell_return_chbx)
    CheckBox viewSellReturnChbx;

    @BindView(R.id.all_cash_register_cbox)
    CheckBox allCashRegisterCbox;
    @BindView(R.id.view_cash_register_cbox)
    CheckBox viewCashRegisterCbox;
    @BindView(R.id.close_cash_register_cbox)
    CheckBox closeCashRegisterCbox;

    @BindView(R.id.brand_all_chbx)
    CheckBox allBrandCbox;
    @BindView(R.id.delete_brand_chbx)
    CheckBox deleteBrandCbox;
    @BindView(R.id.add_brand_chbx)
    CheckBox addBrandCbox;
    @BindView(R.id.edit_brand_chbx)
    CheckBox editBrandCbox;
    @BindView(R.id.view_brand_chbx)
    CheckBox viewBrandCbox;

    @BindView(R.id.tax_rate_all_chbx)
    CheckBox allTaxRateCbox;
    @BindView(R.id.delete_tax_rate_chbx)
    CheckBox deleteTaxRateCbox;
    @BindView(R.id.add_tax_rate_chbx)
    CheckBox addTaxRateCbox;
    @BindView(R.id.edit_tax_rate_chbx)
    CheckBox editTaxRateCbox;
    @BindView(R.id.view_tax_rate_chbx)
    CheckBox viewTaxRateCbox;

    @BindView(R.id.unit_all_chbx)
    CheckBox allUnitCbox;
    @BindView(R.id.delete_unit_chbx)
    CheckBox deleteUnitCbox;
    @BindView(R.id.add_unit_chbx)
    CheckBox addUnitCbox;
    @BindView(R.id.edit_unit_chbx)
    CheckBox editUnitCbox;
    @BindView(R.id.view_unit_chbx)
    CheckBox viewUnitCbox;

    @BindView(R.id.category_all_chbx)
    CheckBox allCategoryCbox;
    @BindView(R.id.delete_category_chbx)
    CheckBox deleteCategoryCbox;
    @BindView(R.id.add_category_chbx)
    CheckBox addCategoryCbox;
    @BindView(R.id.edit_category_chbx)
    CheckBox editCategoryCbox;
    @BindView(R.id.view_category_chbx)
    CheckBox viewCategoryCbox;

    @BindView(R.id.business_setting_all_chbx)
    CheckBox allBusiness_settingCbox;
    @BindView(R.id.access_business_setting_chbx)
    CheckBox accessBusinessSettingChbx;
    @BindView(R.id.access_expenses_chbx)
    CheckBox accessExpensesChbx;
    @BindView(R.id.access_report_chbx)
    CheckBox accessReportChbx;

    @BindView(R.id.business_location_all_chbx)
    CheckBox allBusiness_locationCbox;
    @BindView(R.id.delete_business_location_chbx)
    CheckBox deleteBusiness_locationCbox;
    @BindView(R.id.add_business_location_chbx)
    CheckBox addBusiness_locationCbox;
    @BindView(R.id.edit_business_location_chbx)
    CheckBox editBusiness_locationCbox;
    @BindView(R.id.view_business_location_chbx)
    CheckBox viewBusiness_locationCbox;

    @BindView(R.id.variation_all_chbx)
    CheckBox allVariationCbox;
    @BindView(R.id.delete_variation_chbx)
    CheckBox deleteVariationCbox;
    @BindView(R.id.add_variation_chbx)
    CheckBox addVariationCbox;
    @BindView(R.id.edit_variation_chbx)
    CheckBox editVariationCbox;
    @BindView(R.id.view_variation_chbx)
    CheckBox viewVariationCbox;

    @BindView(R.id.report_all_chbx)
    CheckBox reportAllChbx;
    @BindView(R.id.view_purchase_loss_chbx)
    CheckBox viewPurchaseLossChbx;
    @BindView(R.id.view_product_stock_chbx)
    CheckBox viewProductStockChbx;

    @BindView(R.id.all_stock_chbx)
    CheckBox allStockChbx;
    @BindView(R.id.view_stock_transfert_chbx)
    CheckBox viewStockTransfertChbx;
    @BindView(R.id.view_stock_adj_chbx)
    CheckBox viewStockAdjChbx;

    @BindView(R.id.warranty_all_chbx)
    CheckBox allWarrantyCbox;
    @BindView(R.id.delete_warranty_chbx)
    CheckBox deleteWarrantyCbox;
    @BindView(R.id.add_warranty_chbx)
    CheckBox addWarrantyCbox;
    @BindView(R.id.edit_warranty_chbx)
    CheckBox editWarrantyCbox;
    @BindView(R.id.view_warranty_chbx)
    CheckBox viewWarrantyCbox;

    private ArrayList<Business_location> currentBusinesslocation = null;

    public AddRoleFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_add_roles, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();

        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();

        userDbController = new UserDbController(_context);
        userDbController.open();

        permissionDbController = new PermissionDbController(_context);
        permissionDbController.open();

        roleDbController = new RoleDbController(_context);
        roleDbController.open();

        roleHasPermissionDbController = new RoleHasPermissionDbController(_context);
        roleHasPermissionDbController.open();

        currentBusinesslocation = businessLocationDbController.getAllStationSpinner();
        ArrayList<String> dataStation = new ArrayList<>();

        if (currentBusinesslocation.size() > 0) {
            for (Business_location businesslocation : currentBusinesslocation) {
                dataStation.add(businesslocation.getName());
            }
            setSpinnerStation(dataStation);
        }

        initListners();
        return root;
    }

    private void initListners() {
        userAllChbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                viewUserChbox.setChecked(true);
                editUserChbox.setChecked(true);
                deletUserChbox.setChecked(true);
                addUserChbox.setChecked(true);
                // perform logic
            } else {
                viewUserChbox.setChecked(false);
                editUserChbox.setChecked(false);
                deletUserChbox.setChecked(false);
                addUserChbox.setChecked(false);
            }
        });
        allRoleCbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                viewRoleCbox.setChecked(true);
                editRoleCbox.setChecked(true);
                deleteRoleCbox.setChecked(true);
                addRoleCbox.setChecked(true);
                // perform logic
            } else {
                viewRoleCbox.setChecked(false);
                editRoleCbox.setChecked(false);
                deleteRoleCbox.setChecked(false);
                addRoleCbox.setChecked(false);
            }
        });
        allSupplierCbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                viewContactCbox.setChecked(true);
                editContactCbox.setChecked(true);
                deleteContactCbox.setChecked(true);
                addContactCbox.setChecked(true);
                // perform logic
            } else {
                viewContactCbox.setChecked(false);
                editContactCbox.setChecked(false);
                deleteContactCbox.setChecked(false);
                addContactCbox.setChecked(false);
            }
        });
        allCustomerCbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                viewCustomerCbox.setChecked(true);
                editCustomerCbox.setChecked(true);
                deleteCustomerCbox.setChecked(true);
                addCustomerCbox.setChecked(true);
                // perform logic
            } else {
                viewCustomerCbox.setChecked(false);
                editCustomerCbox.setChecked(false);
                deleteCustomerCbox.setChecked(false);
                addCustomerCbox.setChecked(false);
            }
        });
        allProductCbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                viewProductCbox.setChecked(true);
                editProductCbox.setChecked(true);
                deleteProductCbox.setChecked(true);
                addProductCbox.setChecked(true);
                addOpningStockCbox.setChecked(true);
                viewPurchasePriceCbox.setChecked(true);
                // perform logic
            } else {
                viewProductCbox.setChecked(false);
                editProductCbox.setChecked(false);
                deleteProductCbox.setChecked(false);
                addProductCbox.setChecked(false);
                addOpningStockCbox.setChecked(false);
                viewPurchasePriceCbox.setChecked(false);
            }
        });
        allPurchaseStockAdjCbox.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked) {
                viewPurchaseStockAdjCbox.setChecked(true);
                editPurchaseStockAdjCbox.setChecked(true);
                deletePurchaseStockAdjCbox.setChecked(true);
                addPurchaseStockAdjCbox.setChecked(true);
                viewAddEditDeletPaymentAdjCbox.setChecked(true);
                ownPurchaseCbox.setChecked(true);
                // perform logic
            } else {
                viewPurchaseStockAdjCbox.setChecked(false);
                editPurchaseStockAdjCbox.setChecked(false);
                deletePurchaseStockAdjCbox.setChecked(false);
                addPurchaseStockAdjCbox.setChecked(false);
                viewAddEditDeletPaymentAdjCbox.setChecked(false);
                ownPurchaseCbox.setChecked(false);
            }
        });
        allSellCbox.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked) {
                viewPosSellCbox.setChecked(true);
                editPosSelljCbox.setChecked(true);
                deletePosSelljCbox.setChecked(true);
                addPosSellCbox.setChecked(true);
                listQuotationCbox.setChecked(true);
                viewListDraftCbox.setChecked(true);
                listDiscountCbox.setChecked(true);
                viewSellReturnChbx.setChecked(true);
                // perform logic
            } else {
                viewPosSellCbox.setChecked(false);
                editPosSelljCbox.setChecked(false);
                deletePosSelljCbox.setChecked(false);
                addPosSellCbox.setChecked(false);
                listQuotationCbox.setChecked(false);
                viewListDraftCbox.setChecked(false);
                listDiscountCbox.setChecked(false);
                viewSellReturnChbx.setChecked(false);
            }
        });
        allCashRegisterCbox.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked) {
                viewCashRegisterCbox.setChecked(true);
                closeCashRegisterCbox.setChecked(true);

                // perform logic
            } else {
                viewCashRegisterCbox.setChecked(false);
                closeCashRegisterCbox.setChecked(false);
            }
        });
        allBrandCbox.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked) {
                viewBrandCbox.setChecked(true);
                editBrandCbox.setChecked(true);
                deleteBrandCbox.setChecked(true);
                addBrandCbox.setChecked(true);

                // perform logic
            } else {
                viewBrandCbox.setChecked(false);
                editBrandCbox.setChecked(false);
                deleteBrandCbox.setChecked(false);
                addBrandCbox.setChecked(false);
            }
        });
        allTaxRateCbox.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked) {
                viewTaxRateCbox.setChecked(true);
                editTaxRateCbox.setChecked(true);
                deleteTaxRateCbox.setChecked(true);
                addTaxRateCbox.setChecked(true);

                // perform logic
            } else {
                viewTaxRateCbox.setChecked(false);
                editTaxRateCbox.setChecked(false);
                deleteTaxRateCbox.setChecked(false);
                addTaxRateCbox.setChecked(false);
            }
        });
        allUnitCbox.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked) {
                viewUnitCbox.setChecked(true);
                editUnitCbox.setChecked(true);
                deleteUnitCbox.setChecked(true);
                addUnitCbox.setChecked(true);

                // perform logic
            } else {
                viewUnitCbox.setChecked(false);
                editUnitCbox.setChecked(false);
                deleteUnitCbox.setChecked(false);
                addUnitCbox.setChecked(false);
            }
        });
        allCategoryCbox.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked) {
                viewCategoryCbox.setChecked(true);
                editCategoryCbox.setChecked(true);
                deleteCategoryCbox.setChecked(true);
                addCategoryCbox.setChecked(true);

                // perform logic
            } else {
                viewCategoryCbox.setChecked(false);
                editCategoryCbox.setChecked(false);
                deleteCategoryCbox.setChecked(false);
                addCategoryCbox.setChecked(false);
            }
        });
        allBusiness_settingCbox.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked)
            {
                accessBusinessSettingChbx.setChecked(true);
                accessExpensesChbx.setChecked(true);
                accessReportChbx.setChecked(true);

                // perform logic
            }
            else
            {
                accessBusinessSettingChbx.setChecked(false);
                accessExpensesChbx.setChecked(false);
                accessReportChbx.setChecked(false);
            }
        });
        allBusiness_locationCbox.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked) {
                viewBusiness_locationCbox.setChecked(true);
                editBusiness_locationCbox.setChecked(true);
                deleteBusiness_locationCbox.setChecked(true);
                addBusiness_locationCbox.setChecked(true);

                // perform logic
            } else {
                viewBusiness_locationCbox.setChecked(false);
                editBusiness_locationCbox.setChecked(false);
                deleteBusiness_locationCbox.setChecked(false);
                addBusiness_locationCbox.setChecked(false);
            }
        });

        allVariationCbox.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked) {
                viewVariationCbox.setChecked(true);
                editVariationCbox.setChecked(true);
                deleteVariationCbox.setChecked(true);
                addVariationCbox.setChecked(true);

                // perform logic
            } else {
                viewVariationCbox.setChecked(false);
                editVariationCbox.setChecked(false);
                deleteVariationCbox.setChecked(false);
                addVariationCbox.setChecked(false);
            }
        });

        reportAllChbx.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked) {
                viewPurchaseLossChbx.setChecked(true);
                viewProductStockChbx.setChecked(true);


                // perform logic
            } else {
                viewPurchaseLossChbx.setChecked(false);
                viewProductStockChbx.setChecked(false);

            }
        });

        allStockChbx.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked) {
                viewStockTransfertChbx.setChecked(true);
                viewStockAdjChbx.setChecked(true);
                // perform logic
            } else {
                viewStockTransfertChbx.setChecked(false);
                viewStockAdjChbx.setChecked(false);
            }
        });

        allWarrantyCbox.setOnCheckedChangeListener((buttonView, isChecked) ->
        {
            if (isChecked) {
                viewWarrantyCbox.setChecked(true);
                addWarrantyCbox.setChecked(true);
                editWarrantyCbox.setChecked(true);
                deleteWarrantyCbox.setChecked(true);
                // perform logic
            } else {
                viewWarrantyCbox.setChecked(false);
                addWarrantyCbox.setChecked(false);
                editWarrantyCbox.setChecked(false);
                deleteWarrantyCbox.setChecked(false);
            }
        });


        addBtn.setOnClickListener(v ->
        {
            if (nameEdittext.getText().toString().matches(""))
            {
                Toast.makeText(getContext(), "Enter Role Name ", Toast.LENGTH_LONG).show();
            }
            else
            {
            Role role = new Role();
            role.setName(nameEdittext.getText().toString());
            role.setGuard_name("mobile");
            // todo make general business id
            role.setBusiness_id(1);
            role.setIs_default(0);
            role.setIs_service_staff(0);

            int idInsert = roleDbController.insertLocal(role);

            if (idInsert > 0) {
                ArrayList<Role> roleArrayList = new ArrayList<>();
                //TODO add_purchase_stockadj_cbox
                //TODO cash_register

                if (viewUserChbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewUserChbox), idInsert));
                if (addUserChbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addUserChbox), idInsert));
                if (editUserChbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editUserChbox), idInsert));
                if (deletUserChbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deletUserChbox), idInsert));

                if (viewRoleCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewRoleCbox), idInsert));
                if (addRoleCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addRoleCbox), idInsert));
                if (editRoleCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editRoleCbox), idInsert));
                if (deleteRoleCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deleteRoleCbox), idInsert));

//                if (viewCustomerCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewCustomerCbox), idInsert));
//                if (addCustomerCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addCustomerCbox), idInsert));
//                if (editCustomerCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editCustomerCbox), idInsert));
//                if (deleteCustomerCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deleteCustomerCbox), idInsert));

                if (viewContactCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewContactCbox), idInsert));
                if (addContactCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addContactCbox), idInsert));
                if (editContactCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editContactCbox), idInsert));
                if (deleteContactCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deleteContactCbox), idInsert));

                if (viewProductCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewProductCbox), idInsert));
                if (addProductCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addProductCbox), idInsert));
                if (editProductCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editProductCbox), idInsert));
                if (deleteProductCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deleteProductCbox), idInsert));

                if (viewPosSellCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewPosSellCbox), idInsert));
                if (addPosSellCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addPosSellCbox), idInsert));
                if (editPosSelljCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editPosSelljCbox), idInsert));
                if (deletePosSelljCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deletePosSelljCbox), idInsert));
                if (viewListDraftCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewListDraftCbox), idInsert));
                if (listQuotationCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(listQuotationCbox), idInsert));
                if (listDiscountCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(listDiscountCbox), idInsert));
                if (viewSellReturnChbx.isChecked()) roleArrayList.add(new Role(getPermissionId(viewSellReturnChbx), idInsert));

                if (viewCashRegisterCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewCashRegisterCbox), idInsert));
                if (closeCashRegisterCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(closeCashRegisterCbox), idInsert));

                if (viewBrandCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewBrandCbox), idInsert));
                if (addBrandCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addBrandCbox), idInsert));
                if (editBrandCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editBrandCbox), idInsert));
                if (deleteBrandCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deleteBrandCbox), idInsert));

                if (viewTaxRateCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewTaxRateCbox), idInsert));
                if (addTaxRateCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addTaxRateCbox), idInsert));
                if (editTaxRateCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editTaxRateCbox), idInsert));
                if (deleteTaxRateCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deleteTaxRateCbox), idInsert));

                if (viewUnitCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewUnitCbox), idInsert));
                if (addUnitCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addUnitCbox), idInsert));
                if (editUnitCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editUnitCbox), idInsert));
                if (deleteUnitCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deleteUnitCbox), idInsert));

                if (viewCategoryCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewCategoryCbox), idInsert));
                if (addCategoryCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addCategoryCbox), idInsert));
                if (editCategoryCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editCategoryCbox), idInsert));
                if (deleteCategoryCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deleteCategoryCbox), idInsert));

                if (accessBusinessSettingChbx.isChecked()) roleArrayList.add(new Role(getPermissionId(accessBusinessSettingChbx), idInsert));
                if (accessExpensesChbx.isChecked()) roleArrayList.add(new Role(getPermissionId(accessExpensesChbx), idInsert));
                if (accessReportChbx.isChecked()) roleArrayList.add(new Role(getPermissionId(accessReportChbx), idInsert));

                if (viewBusiness_locationCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewBusiness_locationCbox), idInsert));
                if (addBusiness_locationCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addBusiness_locationCbox), idInsert));
                if (editBusiness_locationCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editBusiness_locationCbox), idInsert));
                if (deleteBusiness_locationCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deleteBusiness_locationCbox), idInsert));


                if (viewPurchaseStockAdjCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewPurchaseStockAdjCbox), idInsert));
                if (addPurchaseStockAdjCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addPurchaseStockAdjCbox), idInsert));
                if (editPurchaseStockAdjCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editPurchaseStockAdjCbox), idInsert));
                if (deletePurchaseStockAdjCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deletePurchaseStockAdjCbox), idInsert));


                if (viewVariationCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewVariationCbox), idInsert));
                if (addVariationCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addVariationCbox), idInsert));
                if (editVariationCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editVariationCbox), idInsert));
                if (deleteVariationCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deleteVariationCbox), idInsert));

                if (viewPurchaseLossChbx.isChecked()) roleArrayList.add(new Role(getPermissionId(viewPurchaseLossChbx), idInsert));
                if (viewProductStockChbx.isChecked()) roleArrayList.add(new Role(getPermissionId(viewProductStockChbx), idInsert));

                if (viewStockTransfertChbx.isChecked()) roleArrayList.add(new Role(getPermissionId(viewStockTransfertChbx), idInsert));
                if (viewStockAdjChbx.isChecked()) roleArrayList.add(new Role(getPermissionId(viewStockAdjChbx), idInsert));

                if (viewWarrantyCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(viewWarrantyCbox), idInsert));
                if (addWarrantyCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(addWarrantyCbox), idInsert));
                if (editWarrantyCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(editWarrantyCbox), idInsert));
                if (deleteWarrantyCbox.isChecked()) roleArrayList.add(new Role(getPermissionId(deleteWarrantyCbox), idInsert));



                roleHasPermissionDbController.fill(roleArrayList);
                Toast.makeText(getContext(), getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                replaceFragment(new RolesFragment());
            }

            }
        });

//        birthDateTxt.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
//                        new DatePickerDialog.OnDateSetListener() {
//                            @Override
//                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
//                                String myFormat = populateSetDate(year, month, day);
//                                //currentHashMap.put("dob", myFormat);
//                                birthDateTxt.setText(myFormat);
//                            }
//                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
//                datePickerDialog.show();
//            }
//        });


//        addBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (spinner_role.getSelectedItemPosition() != 0
//                && !emailEdittext.getText().toString().matches("")
//                && !usernameEdittext.getText().toString().matches("")
//                && !passwordEdittext.getText().toString().matches("")
//                && !passwordConfirmEdittext.getText().toString().matches("")
//                && passwordEdittext.getText().toString().matches(passwordConfirmEdittext.getText().toString())) {
//                    postUser();
//                } else {
//                    Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
//                }
//            }
//        });

        backBtn.setOnClickListener(v->{
            replaceFragment(new RolesFragment());
        });
    }

    private void postUser() {

//        User user=new User();
//        user.setSurname(prefixEdittext.getText().toString());
//        user.setFirst_name(firstnameEdittext.getText().toString());
//        user.setLast_name(lastName.getText().toString());
//        user.setEmail(emailEdittext.getText().toString());
//        user.setStatus(isactiveCheckbox.isChecked()+"");
//        user.setUsername(usernameEdittext.getText().toString());
//        user.setPassword(passwordEdittext.getText().toString());
//        user.setDob(birthDateTxt.getText().toString());
//       if (spinnerGender.getSelectedItemPosition()!=0)  user.setGender(spinnerGender.getSelectedItem().toString());
//       if (spinnerMaritalStatus.getSelectedItemPosition()!=0)  user.setMarital_status(spinnerMaritalStatus.getSelectedItem().toString());
//        user.setBlood_group(bloodgroupEditText.getText().toString());
//        user.setContact_number(contactnumberEditText.getText().toString());
//        user.setAddress(currentadressEditText.getText().toString());
//
//        int insertedId=userDbController.insertLocal(user);
//        if(insertedId>0)
//        {
//            Toast.makeText(getContext(), getContext().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
//            replaceFragment(new UsersFragment());
//        }
//        else
//        {
//            Toast.makeText(getContext(), getContext().getString(R.string.lbl_error_insert), Toast.LENGTH_LONG).show();
//        }

    }

    private int getPermissionId(CheckBox viewProductCbox)
    {
       return  permissionDbController.getPermissionsId(viewProductCbox.getTag().toString());
    }

    private void setSpinnerStation(ArrayList<String> data) {

//        spinnerStation.setItems(data);
//        spinnerStation.hasNoneOption(true);
//        // spinnerStation.setSelection(new int[]{0});
//        spinnerStation.setListener(this);
      /*
        ArrayAdapter<String> spinnerArrayAdapter = new ArrayAdapter<String>(
                this, R.layout.simple_spinner_items, data);
        spinnerStation.setAdapter(spinnerArrayAdapter);*/
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


}