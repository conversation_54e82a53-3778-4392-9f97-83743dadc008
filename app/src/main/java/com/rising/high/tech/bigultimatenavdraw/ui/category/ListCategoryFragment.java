package com.rising.high.tech.bigultimatenavdraw.ui.category;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.ui.category.adapter.CategoryAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.Constant;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import java.util.ArrayList;
import java.util.Objects;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CATEGORY_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.LOCAL_USE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PRODUCT_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;

public class ListCategoryFragment extends Fragment  implements  AdapterView.OnItemSelectedListener {

    private static final String TAG = "ListCategoryFragment";
    private Context _context;

    @BindView(R.id.id_search_edit)
    SearchView searchEdit;
    @BindView(R.id.noItemFound)
    TextView noItemFound;
    RecyclerView recyclerCategory;
    Spinner spinnerCategory, spinnerStation, spinnerUnit;
    Button btnFilter, btnAdd;
    int parentCategoryId = 0;
    CategoryDbController categoryDbController;
    private ArrayList<Category> dataList = new ArrayList<>();
    private Integer userId;
    CategoryAdapter categoryAdapter;
    SessionManager session;
    public ListCategoryFragment() {
        // Required empty public constructor
    }
    private ArrayList<Category> parentCategoryList;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_list_category, container, false);
        ButterKnife.bind(this, root);

        recyclerCategory = root.findViewById(R.id.recycle_category);
        spinnerCategory = root.findViewById(R.id.spinner_category);
        spinnerStation = root.findViewById(R.id.spinner_station);
        spinnerUnit = root.findViewById(R.id.spinner_unit);
        btnFilter = root.findViewById(R.id.id_filter);
        btnAdd = root.findViewById(R.id.id_add);

        _context = getContext();
        session = new SessionManager(_context);
        checkRoles();


        categoryDbController = new CategoryDbController(_context);
        categoryDbController.open();
        setUpRecyclerView();

        initListners();
        return root;
    }


    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER)  || !session.checkPermissionSubModule(CATEGORY_ADD))
        {
            btnAdd.setVisibility(View.INVISIBLE);
        }
    }

    private void initListners(){
        btnAdd.setOnClickListener(v -> addCategory());
        userId = (int) session.getUserDetails().get(session.ID_USER);

        searchEdit.setQueryHint("Search Here");
        searchEdit.setOnQueryTextListener(new SearchView.OnQueryTextListener() {

            @Override
            public boolean onQueryTextSubmit(String query) {
                categoryAdapter.setData(categoryDbController.getCategoriesLike(query));
                setItemView();
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                categoryAdapter.setData(categoryDbController.getCategoriesLike(newText));
                setItemView();
                return false;
            }
        });
    }
    private void setUpRecyclerView()
    {

        categoryAdapter = new CategoryAdapter();
        dataList= categoryDbController.getAllCategory();
        categoryAdapter.setData(dataList);
        categoryAdapter.setonClickAction(new CategoryAdapter.onClickAction() {

            @Override
            public void onClickEdit(Category category) {
                editCategory(category);
            }

            @Override
            public void onClickDelete(int position,Category category) {
                deleteItem(position,category);
            }
        });

        recyclerCategory.setAdapter(categoryAdapter);
        recyclerCategory.setLayoutManager(new LinearLayoutManager(_context));
        categoryAdapter.notifyDataSetChanged();
        setItemView();
    }
    public void setItemView()
    {
        if(categoryAdapter.getItemCount() > 0)
        {
            recyclerCategory.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        }
        else
        {
            recyclerCategory.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }



    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        if (parent.getId() == R.id.spinnerParentCategory) {
            parentCategoryId = parentCategoryList.get(position).getId();
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }
    private void deleteItem(int position,Category category) {

        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(_context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());

        ButtonSave.setOnClickListener(v -> {
            System.out.println("category.getId() "+ category.getId());

            categoryDbController.deleteItem(category.getId());
            dataList.remove(position);
            mAlertDialog.dismiss();
            categoryAdapter.notifyDataSetChanged();
            setItemView();

        });
        mAlertDialog.show();
    }
    private void addCategory() {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.category_add_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                _context);

        alertDialogBuilder.setView(promptsView);

        final EditText catgeroyCode = promptsView.findViewById(R.id.id_category_code);
        final EditText catgeroyName = promptsView.findViewById(R.id.catgeroy_name);
        final EditText desc = promptsView.findViewById(R.id.id_desc);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);
        final CheckBox addSubcategoryCheckbox = promptsView.findViewById(R.id.addSubcategoryCheckbox);
        final FrameLayout ParentCategoryLayout = promptsView.findViewById(R.id.ParentCategoryLayout);
        final Spinner spinnerParentCategory = promptsView.findViewById(R.id.spinnerParentCategory);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        addSubcategoryCheckbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if(isChecked) {
                ParentCategoryLayout.setVisibility(View.VISIBLE);
            } else {
                ParentCategoryLayout.setVisibility(View.GONE);
            }});

        parentCategoryList = categoryDbController.getAllMainCategory();
        if(parentCategoryList.size() == 0)
        {
            addSubcategoryCheckbox.setVisibility(View.GONE);
        }
        ArrayList<String> categoryList = new ArrayList<>();
        for (int i = 0; i < parentCategoryList.size(); i++) {
            categoryList.add(parentCategoryList.get(i).getName());
        }
        ArrayAdapter<String> dataAdapter = new ArrayAdapter<>(_context, android.R.layout.simple_spinner_item, categoryList);
        dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerParentCategory.setAdapter(dataAdapter);
        spinnerParentCategory.setOnItemSelectedListener(this);

        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());
        ButtonSave.setOnClickListener(v -> {

            if (catgeroyName.getText().toString().isEmpty()) {
                catgeroyName.requestFocus();
                catgeroyName.setError(getString(R.string.select_category_name));
            }
            else
            {
                Category category = new Category();
                category.setBusiness_id(session.getBusinessModel().getId());
                category.setCreated_by(userId);
                category.setName(catgeroyName.getText().toString());
                category.setDescription(desc.getText().toString());
                category.setShort_code(catgeroyCode.getText().toString());
                category.setCategory_type(Constant.CATEGORY_TYPE);
                category.setIs_sync("no");
                if(addSubcategoryCheckbox.isChecked())
                {
                    category.setParent_id(parentCategoryId);
                }
                int inserted = categoryDbController.insertLocal(category);
                if (inserted > 0) {
                    FileUtil.showDialog(_context,getString(R.string.success),getResources().getString(R.string.categories_added_success ));
                    dataList= categoryDbController.getAllCategory();
                    categoryAdapter.setData(dataList);
                    categoryAdapter.notifyDataSetChanged();
                    parentCategoryId=0;
                    mAlertDialog.dismiss();
                } else {
                    StringFormat.showSnackBar(promptsView, R.string.failed_to_update_data,true);
                }
            }

            setItemView();

        });


        mAlertDialog.show();
    }
    private void editCategory(Category category) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.category_add_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(_context);
        alertDialogBuilder.setView(promptsView);

        final EditText catgeroyCode = promptsView.findViewById(R.id.id_category_code);
        final EditText catgeroyName = promptsView.findViewById(R.id.catgeroy_name);
        final EditText desc = promptsView.findViewById(R.id.id_desc);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);
        final CheckBox addSubcategoryCheckbox = promptsView.findViewById(R.id.addSubcategoryCheckbox);
        final FrameLayout ParentCategoryLayout = promptsView.findViewById(R.id.ParentCategoryLayout);
        final Spinner spinnerParentCategory = promptsView.findViewById(R.id.spinnerParentCategory);

        parentCategoryList = categoryDbController.getAllMainCategory();
        ArrayList<String> categoryList = new ArrayList<>();
        for (int i = 0; i < parentCategoryList.size(); i++) {
            categoryList.add(parentCategoryList.get(i).getName());
        }
        ArrayAdapter<String> dataAdapter = new ArrayAdapter<>(_context, android.R.layout.simple_spinner_item, categoryList);
        dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerParentCategory.setAdapter(dataAdapter);
        spinnerParentCategory.setOnItemSelectedListener(this);
        if(category.getParent_id() != 0)
        {
            addSubcategoryCheckbox.setChecked(true);
            ParentCategoryLayout.setVisibility(View.VISIBLE);
            Category parentCategoryName = categoryDbController.getCategoryById(category.getParent_id());
            spinnerParentCategory.setSelection(dataAdapter.getPosition(parentCategoryName.getName()));
            parentCategoryId=category.getParent_id();
        }
        else
        {
            parentCategoryId = 0;
        }

        addSubcategoryCheckbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if(isChecked) {
                ParentCategoryLayout.setVisibility(View.VISIBLE);
            } else {
                ParentCategoryLayout.setVisibility(View.GONE);
            }});


        ButtonSave.setText(_context.getResources().getString(R.string.label_updatee));
        catgeroyName.setText(category.getName());
        catgeroyCode.setText(category.getShort_code() !=null ?category.getShort_code() :"");
        desc.setText(category.getDescription() !=null ?category.getDescription() :"");

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());

        ButtonSave.setOnClickListener(v -> {
            if (catgeroyName.getText().toString().isEmpty()) {
                catgeroyName.requestFocus();
                catgeroyName.setError(getString(R.string.select_category_name));
            }
            else
            {
                category.setName(catgeroyName.getText().toString());
                category.setDescription(desc.getText().toString());
                category.setShort_code(catgeroyCode.getText().toString());
                if(addSubcategoryCheckbox.isChecked())
                {
                    category.setParent_id(parentCategoryId);
                }
                int inserted = categoryDbController.editCategory(category);
                if (inserted > 0) {
                    FileUtil.showDialog(_context,_context.getString(R.string.success),_context.getResources().getString(R.string.categories_updated_success ));
                    mAlertDialog.dismiss();
                    parentCategoryId=0;
                    dataList=categoryDbController.getAllCategory();
                    categoryAdapter.setData(dataList);
                    categoryAdapter.notifyDataSetChanged();
                } else {
                    StringFormat.showSnackBar(promptsView, R.string.failed_to_update_data,true);
                }
            }

        });


        mAlertDialog.show();
    }

}