package com.rising.high.tech.bigultimatenavdraw.ui.product;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.Gallery;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.google.gson.Gson;
// Removed zxing imports
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BrandDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TaxRatesDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UnitDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Product_location;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.ImageAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinCategoryAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinTaxRatesAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinUnitAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.category.ListCategoryFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.unit.ListUnitFragment;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.MultiSelectSpinner;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.io.Console;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

import static android.app.Activity.RESULT_OK;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.EXCLUSIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.INCLUSIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SINGLE;

public class EditProductFragment extends Fragment implements MultiSelectSpinner.OnMultipleItemsSelectedListener, AdapterView.OnItemSelectedListener {
    private static final String TAG = "ListProductFragment";
    private static final int PICK_FILE_REQUEST = 1;

    private Context _context;
    private SpinStationAdapter spinStationAdapter;

    private BusinessLocationDbController businessLocationDbController;
    private ProductLocationDbController productLocationDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private CategoryDbController categoryDbController;
    private BrandDbController brandDbController;
    private UnitDbController unitDbController;
    private ProductDbController productDbController;
    private VariationsDbController variationsDbController;
    TaxRatesDbController taxRatesDbController;
    Spinner subCategorySpinner, spinnerCategory, spinnerTaxRates, spinnerUnit, spinProductType;
    EditText product_weight, dspIncTax, dppExcTax, productName, dspExcTax, editMs, dppIncTax, editMargin, alertQuantity, productDesc;
    LinearLayout mStockContainer;
    AppCompatTextView imageAddAttach;
    Gallery gallery;
    TextView subTitle;
    SpinCategoryAdapter spinCategoryAdapter;
    SpinUnitAdapter spinUnitAdapter;
    Spinner spinnerBrand, spinnerBarcodeType;
    Bitmap productImage;
    int subCategoryId = 0;
    private Integer userId;

    private ArrayList<Business_location> currentBusinesslocation = null;
    private ArrayList<Business_location> selectedBusinesslocations = null;

    @BindView(R.id.id_seling_tax_price_spinner)
    Spinner spinnerSelingTaxPriceSpinner;
    @BindView(R.id.spinner_station)
    MultiSelectSpinner spinnerStation;
    @BindView(R.id.product_sku)
    EditText productSku;
    @BindView(R.id.id_back)
    Button btnBack;
    @BindView(R.id.add_btn)
    Button btnAdd;
    @BindView(R.id.id_add_unit)
    ImageView btnAddUnit;
    @BindView(R.id.id_add_category)
    ImageView btnAddCategory;

    private ArrayList<Tax_rates> taxList;
    private ArrayList<Brand> brandsList;
    private SessionManager session;
    private int indexId = 0;

    int brandId = 0, taxId = 0;
    CheckBox cbManageStock, cbNotFSelling;
    private static String[] PERMISSIONS_STORAGE = {
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE};
    private static final int REQUEST_EXTERNAL_STORAGE = 1;

    public EditProductFragment() {
        // Required empty public constructor
    }

    SpinTaxRatesAdapter spinTaxRatesAdapter;
    private ArrayList<Category> categoryList;
    private ArrayList<Category> subCategoryList = new ArrayList<>();
    ArrayAdapter<String> spinnerSubCategoryAdapter;
    ArrayAdapter<String> spinnerBrandAdapter;
    Boolean isSelectedStation = false;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.activity_add_product, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        session = new SessionManager(_context);
        userId = (int) session.getUserDetails().get(session.ID_USER);

        Bundle args = getArguments();
        assert args != null;
        indexId = args.getInt("id", 0);

        spinnerCategory = root.findViewById(R.id.spinner_category);
        spinnerBrand = root.findViewById(R.id.spinner_brand);
        cbManageStock = root.findViewById(R.id.id_manage_stock);
        cbNotFSelling = root.findViewById(R.id.id_not_for_selling);
        gallery = root.findViewById(R.id.gallery);
        alertQuantity = root.findViewById(R.id.edit_alert_quantity);
        productDesc = root.findViewById(R.id.produxt_desc);
        spinProductType = root.findViewById(R.id.product_type_spin);
        subTitle = root.findViewById(R.id.id_sub_title);
        dspIncTax = root.findViewById(R.id.default_sell_inc_tax);
        dppExcTax = root.findViewById(R.id.id_exc_tax);
        dspExcTax = root.findViewById(R.id.id_default_sel_price_exc_tax);
        spinnerTaxRates = root.findViewById(R.id.tax_rate);
        productName = root.findViewById(R.id.product_name);
        spinnerUnit = root.findViewById(R.id.spinner_unit);
        mStockContainer = root.findViewById(R.id.manage_stock_container);
        product_weight = root.findViewById(R.id.product_weight);
        spinnerBarcodeType = root.findViewById(R.id.spinnerBarcodeType);
        dppIncTax = root.findViewById(R.id.id_inc_tax);
        editMargin = root.findViewById(R.id.id_margin);
        imageAddAttach = root.findViewById(R.id.id_image_add_attach);
        subCategorySpinner = root.findViewById(R.id.subCategorySpinner);

        initDB();
        initSpinners();
        initForm();
        initClickListners();

        btnAdd.setText(getResources().getString(R.string.label_updatee));
        subTitle.setText(getResources().getString(R.string.label_update_new_product));

        return root;
    }

    private void initClickListners() {

        btnBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListProductFragment());
            }
        });

        btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {


                for (Business_location businesslocation1 : currentBusinesslocation) {
                    if (businesslocation1.getId() > 0 && businesslocation1.getSelected() == 1) {
                        isSelectedStation = true;
                        break;
                    }
                }
                if (productName.getText().toString().isEmpty()) {
                    productName.requestFocus();
                    productName.setError(_context.getResources().getString(R.string.string_please_enter_product_name));
                } else if (spinnerUnit.getSelectedItemPosition() == 0) {
                    StringFormat.showSnackBarF(requireView(), R.string.please_select_unit_name, true);
                } else if (spinnerCategory.getSelectedItemPosition() == 0) {
                    StringFormat.showSnackBarF(requireView(), R.string.select_category_name, true);
                } else if (!isSelectedStation) {
                    StringFormat.showSnackBarF(requireView(), R.string.lbl_please_select_station, true);
                } else if (editMargin.getText().toString().isEmpty()) {
                    editMargin.requestFocus();
                    editMargin.setError(_context.getResources().getString(R.string.string_please_enter_margin));
                } else if (dppExcTax.getText().toString().isEmpty()) {
                    dppExcTax.requestFocus();
                    dppExcTax.setError(_context.getResources().getString(R.string.string_please_exclusive_tax));
                } else if (dppIncTax.getText().toString().isEmpty()) {
                    dppIncTax.requestFocus();
                    dppIncTax.setError(_context.getResources().getString(R.string.string_please_einclusive_tax));
                } else {
                    Product product = productDbController.getProductById(indexId);
                    //TODO make it automatic
                    product.setBusiness_id(1);
                    product.setCategory_id(spinCategoryAdapter.getItem(spinnerCategory.getSelectedItemPosition()).getId());
                    product.setSub_category_id(subCategoryId);
                    product.setCategory_name(product.getCategory_name() != null ? product.getCategory_name() : spinCategoryAdapter.getItem(spinnerCategory.getSelectedItemPosition()).getName());
                    product.setName(productName.getText().toString());
                    product.setType(SINGLE);
                    if(spinnerSelingTaxPriceSpinner.getSelectedItemPosition()==0){
                        product.setTax_type(EXCLUSIVE) ;
                    }else if(spinnerSelingTaxPriceSpinner.getSelectedItemPosition()==1) {
                        product.setTax_type(INCLUSIVE);
                    }
                    product.setSku(productSku.getText().toString());
                    product.setNot_for_selling(cbNotFSelling.isChecked() ? 1 : 0);
                    product.setCreated_by(userId);
                    product.setEnable_stock(cbManageStock.isChecked() ? 1 : 0);
                    product.setAlert_quantity(!alertQuantity.getText().toString().equals("") ? Integer.parseInt(alertQuantity.getText().toString()) : 0);
                    product.setEnable_sr_no(0);
                    product.setIs_inactive(0);
                    product.setCategory_id(spinCategoryAdapter.getItem(spinnerCategory.getSelectedItemPosition()).getId());
                    //   product.setUnit_id(spinnerUnit.getSelectedItemPosition());

                    product.setIs_sync("no");
                    product.setUnit_id(spinUnitAdapter.getItem(spinnerUnit.getSelectedItemPosition()).getId());
                    product.setUnit_actualname(spinUnitAdapter.getItem(spinnerUnit.getSelectedItemPosition()).getActual_name());
                    product.setUnit_shortname(spinUnitAdapter.getItem(spinnerUnit.getSelectedItemPosition()).getShort_name());
                    product.setProduct_description(productDesc.getText().toString());
                    product.setBrand_id(brandId);
                    product.setTax(taxId);
                    product.setBarcode_type(spinnerBarcodeType.getSelectedItem().toString());
                    product.setWeight(product_weight.getText().toString());
                    product.setImage_product(productImage);


                    int id = productDbController.updateProduct(product);

                    if (id > 0) {

                        Variation variation = variationsDbController.getVariationByProductId(indexId);
                        variation.setDefault_purchase_price(dppExcTax.getText().toString());
                        variation.setDpp_inc_tax(dppIncTax.getText().toString());
                        variation.setProfit_percent(editMargin.getText().toString());
                        variation.setDefault_sell_price(dspExcTax.getText().toString());
                        variation.setSell_price_inc_tax(dspIncTax.getText().toString());
                        variationsDbController.updateVariation(variation);

                        for (Business_location businesslocation1 : currentBusinesslocation) {
                            productLocationDbController.deleteItem(product.getId(), businesslocation1.getId());

                            Variation_location_details variation_location_detailsOld = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(businesslocation1.getId(), product.getId());
                            variationLocationDetailDbController.deleteItemIdProductIdLocation(product.getId(), businesslocation1.getId());
                            if (businesslocation1.getSelected() == 1) {
                                Product_location product_location = new Product_location(product.getId(), businesslocation1.getId() + "");
                                productLocationDbController.insertLocal(product_location);

                                Variation_location_details variation_location_details = new Variation_location_details();
                                variation_location_details.setProduct_id(product.getId());
                                variation_location_details.setVariation_id(businesslocation1.getId());
                                variation_location_details.setLocation_id(businesslocation1.getId());
                                variation_location_details.setQty_available(variation_location_detailsOld.getQty_available());
                                variation_location_details.setOld_qty_available(variation_location_detailsOld.getOld_qty_available());
                                variationLocationDetailDbController.insertLocal(variation_location_details);
                            }

                        }
                        FileUtil.showDialog(_context, getString(R.string.success), getResources().getString(R.string.products_updated_success));
                        replaceFragment(new ListProductFragment());
                    } else {
                        Toast.makeText(_context, "Error insert", Toast.LENGTH_LONG).show();
                    }

                }

            }
        });

        imageAddAttach.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addAttach();
            }
        });

        cbManageStock.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    mStockContainer.setVisibility(View.VISIBLE);
                    // perform logic

                } else {
                    mStockContainer.setVisibility(View.INVISIBLE);
                    alertQuantity.setText("0");
                }

            }
        });

        dppExcTax.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

                if (!dppExcTax.getText().toString().matches("") && !editMargin.getText().toString().matches("")) {
                    if (!dppExcTax.getText().toString().matches("") && !editMargin.getText().toString().matches("")) {
                        float selling_price_exc_tax;
                        float selling_price_inc_tax;
                        float inc_tax;
                        float margin;
                        // float margin = Float.parseFloat(editMargin.getText().toString());
                        Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                        float tax_perc = Float.parseFloat(tax_rates.getAmount());
                        float inc = Float.parseFloat(dppExcTax.getText().toString());
                        inc_tax = inc + (inc * (tax_perc / 100));
                        dppIncTax.setText(inc_tax + "");

                        if (!editMargin.getText().toString().matches("")) {
                            margin = Float.parseFloat(editMargin.getText().toString());
                            selling_price_exc_tax = inc + (inc * (margin / 100));
                            dspExcTax.setText("" + selling_price_exc_tax);
                            selling_price_inc_tax = inc_tax + (inc_tax * (margin / 100));
                            dspIncTax.setText(selling_price_inc_tax + "");
                        } else {
                            dspExcTax.setText(dppExcTax.getText().toString());
                            dspIncTax.setText(dppExcTax.getText().toString());
                        }
                    }

                }

            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        editMargin.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

                if (!dppExcTax.getText().toString().matches("") && !editMargin.getText().toString().matches("")) {
                    float selling_price_exc_tax;
                    float selling_price_inc_tax;
                    float inc_tax;
                    float margin;
                    // float tax_perc = initProduct.getTax_value();
                    float tax_perc = 0;
                    float inc = Float.parseFloat(dppExcTax.getText().toString());

                    inc_tax = inc + (inc * (tax_perc / 100));
                    dppIncTax.setText(inc_tax + "");
                    if (!editMargin.getText().toString().matches("")) {
                        margin = Float.parseFloat(editMargin.getText().toString());
                        selling_price_exc_tax = inc + (inc * (margin / 100));
                        selling_price_inc_tax = inc_tax + (inc_tax * (margin / 100));
                        dspExcTax.setText("" + selling_price_exc_tax);
                        dspIncTax.setText(selling_price_inc_tax + "");
                    } else {
                        dspExcTax.setText(dppExcTax.getText().toString());
                        dspIncTax.setText(dppExcTax.getText().toString());
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        btnAddUnit.setOnClickListener(v -> {
            replaceFragment(new ListUnitFragment());
        });
        btnAddCategory.setOnClickListener(v -> {
            replaceFragment(new ListCategoryFragment());
        });

    }

    private void initDB() {
        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();

        categoryDbController = new CategoryDbController(_context);
        categoryDbController.open();

        unitDbController = new UnitDbController(_context);
        unitDbController.open();

        productDbController = new ProductDbController(_context);
        productDbController.open();

        variationsDbController = new VariationsDbController(_context);
        variationsDbController.open();

        brandDbController = new BrandDbController(_context);
        brandDbController.open();

        productLocationDbController = new ProductLocationDbController(_context);
        productLocationDbController.open();

        variationLocationDetailDbController = new VariationLocationDetailDbController(_context);
        variationLocationDetailDbController.open();

        taxRatesDbController = new TaxRatesDbController(_context);
        taxRatesDbController.open();
        selectedBusinesslocations = new ArrayList<>();

    }

    private void initSpinners() {
        categoryList = categoryDbController.getAllMainCategory();
        spinCategoryAdapter = new SpinCategoryAdapter(_context, R.layout.custom_spinner_item, categoryList);
        spinnerCategory.setAdapter(spinCategoryAdapter);
        spinnerCategory.setOnItemSelectedListener(this);
        spinUnitAdapter = new SpinUnitAdapter(_context, android.R.layout.simple_spinner_item, unitDbController.getAllUnitSpinner());
        spinnerUnit.setAdapter(spinUnitAdapter);

        currentBusinesslocation = businessLocationDbController.getAllStationSpinner();

        taxList = new ArrayList<>();
        taxList.addAll(taxRatesDbController.getAllTax_ratesSpinner());
        spinTaxRatesAdapter = new SpinTaxRatesAdapter(_context, android.R.layout.simple_spinner_item, taxList);
        spinnerTaxRates.setAdapter(spinTaxRatesAdapter);
        spinnerTaxRates.setOnItemSelectedListener(this);

        brandsList = new ArrayList<>();
        Brand brand = new Brand(0, _context.getResources().getString(R.string.please_select_brand_spin), "");
        brandsList.add(brand);
        brandsList.addAll(brandDbController.getAllBrands());
        ArrayList<String> brands = new ArrayList<>();
        for (int i = 0; i < brandsList.size(); i++) {
            brands.add(brandsList.get(i).getName());
        }
        spinnerBrandAdapter = new ArrayAdapter<>(_context, android.R.layout.simple_spinner_item, brands);
        spinnerBrandAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerBrand.setAdapter(spinnerBrandAdapter);
        spinnerBrand.setOnItemSelectedListener(this);

        subCategoryList = new ArrayList<>();
        Category category = new Category(0, "Please Select");
        subCategoryList.add(category);
        setupSubCategorySpinner();
    }

    public void setupSubCategorySpinner() {
        ArrayList<String> categoryArray = new ArrayList<>();
        for (int i = 0; i < subCategoryList.size(); i++) {
            categoryArray.add(subCategoryList.get(i).getName());
        }
        spinnerSubCategoryAdapter = new ArrayAdapter<>(_context, android.R.layout.simple_spinner_item, categoryArray);
        spinnerSubCategoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        subCategorySpinner.setAdapter(spinnerSubCategoryAdapter);
        subCategorySpinner.setOnItemSelectedListener(this);
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        if (parent.getId() == R.id.spinner_brand) {
            brandId = brandsList.get(position).getId();
        } else if (parent.getId() == R.id.tax_rate) {
            taxId = taxList.get(position).getId();

            if (dppExcTax.getText().toString().length() != 0 && editMargin.getText().toString().length() != 0) {
                float selling_price_exc_tax;
                float selling_price_inc_tax;
                float inc_tax;
                float margin;
                // float margin = Float.parseFloat(editMargin.getText().toString());
                Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                float tax_perc = Float.parseFloat(tax_rates.getAmount());
                float inc = Float.parseFloat(dppExcTax.getText().toString());
                inc_tax = inc + (inc * (tax_perc / 100));
                dppIncTax.setText(inc_tax + "");

                if (!editMargin.getText().toString().matches("")) {
                    margin = Float.parseFloat(editMargin.getText().toString());
                    selling_price_exc_tax = inc + (inc * (margin / 100));
                    dspExcTax.setText("" + selling_price_exc_tax);
                    selling_price_inc_tax = inc_tax + (inc_tax * (margin / 100));
                    dspIncTax.setText(selling_price_inc_tax + "");
                } else {
                    dspExcTax.setText(dppExcTax.getText().toString());
                    dspIncTax.setText(dppExcTax.getText().toString());
                }
            }
        } else if (parent.getId() == R.id.spinner_category) {
            subCategoryList = categoryDbController.getSubCategoryById(categoryList.get(position).getId());
            if (subCategoryList.size() > 0) {
                Category category = categoryDbController.getCategoryById(subCategoryId);
                String subCategoryName = category.getName();
                setupSubCategorySpinner();
                subCategorySpinner.setSelection(spinnerSubCategoryAdapter.getPosition(subCategoryName));
            }
        } else if (parent.getId() == R.id.subCategorySpinner) {
            subCategoryId = subCategoryList.get(position).getId();
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }

    private void initForm() {
        Product product = productDbController.getEditProduct(indexId);
        ArrayList<Product_location> product_locations = productLocationDbController.getProductLocationByProductId(product.getId());
        List<Integer> arrayIndex = new ArrayList<>();
        if (currentBusinesslocation.size() > 0) {
            for (Business_location businesslocation : currentBusinesslocation) {
                for (Product_location product_location : product_locations) {
                    Business_location businesslocation1 = businessLocationDbController.getStationById(Integer.parseInt(product_location.getLocation_id()));
                    if (businesslocation.equals(businesslocation1)) {
                        arrayIndex.add(currentBusinesslocation.indexOf(businesslocation));
                        currentBusinesslocation.get(currentBusinesslocation.indexOf(businesslocation)).setSelected(1);
                    }
                }
            }
        }
        ArrayList<String> dataStation = new ArrayList<>();
        if (currentBusinesslocation.size() > 0) {
            for (Business_location businesslocation : currentBusinesslocation) {
                dataStation.add(businesslocation.getName());
            }
            setSpinnerStation(dataStation, arrayIndex);
        }

        Category category = categoryDbController.getCategoryById(product.getCategory_id());
        if (category != null && categoryList.contains(category)) {
            int spinnerPosition = spinCategoryAdapter.getPosition(category);
            spinnerCategory.setSelection(spinnerPosition);
        }
        subCategoryId = product.getSub_category_id();
        if (product.getTax() != null) {
            spinnerTaxRates.setSelection(product.getTax());
        }
        product_weight.setText(product.getWeight());
        productName.setText(product.getName());
        productDesc.setText(product.getProduct_description());
        productSku.setText(product.getSku());
        alertQuantity.setText(product.getAlert_quantity() + "");


        if (product.getTax_type()!=null){
            if(product.getTax_type().matches(EXCLUSIVE)){
                spinnerSelingTaxPriceSpinner.setSelection(0);
            }else if(product.getTax_type().matches(INCLUSIVE)) {
                spinnerSelingTaxPriceSpinner.setSelection(1);
            }
        }

    //    spinnerSelingTaxPriceSpinner.setPrompt(product.getTax_type());
        productDesc.setText(product.getProduct_description());
        if (product.getImage_product() != null) {
            List<Bitmap> temBitmap = new ArrayList<>();
            temBitmap.add(product.getImage_product());
            ImageAdapter imageAdapter = new ImageAdapter(_context, temBitmap);
            gallery.setAdapter(imageAdapter);
            productImage = product.getImage_product();
        }

        spinProductType.setSelection(0);
        cbManageStock.setChecked(product.getEnable_stock() == 1 ? true : false);
        if (product.getEnable_stock() == 0) mStockContainer.setVisibility(View.INVISIBLE);
        alertQuantity.setText(product.getAlert_quantity() + "");
        cbNotFSelling.setChecked(product.getNot_for_selling() == 1 ? true : false);
        Variation variation = variationsDbController.getVariationByProductId(indexId);
        dppExcTax.setText(variation.getDefault_purchase_price());
        dppIncTax.setText(variation.getDpp_inc_tax());
        editMargin.setText(variation.getProfit_percent());
        dspExcTax.setText(variation.getDefault_sell_price());
        dspIncTax.setText(variation.getSell_price_inc_tax());

        if (product.getBrand_id() != null) {
            Brand brand = brandDbController.getBrandById(product.getBrand_id());
            spinnerBrand.setSelection(spinnerBrandAdapter.getPosition(brand.getName()));
        }

        Unit unit = unitDbController.getUnitsById(product.getUnit_id());
        spinnerUnit.setSelection(spinUnitAdapter.getPosition(unit));


        if (product.getTax_type() != null)
            spinnerSelingTaxPriceSpinner.setSelection(product.getTax_type().equals("exclusive") ? 0 : 1);

    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    private void addAttach() {
        // Check if we have write permission
        int permission = ActivityCompat.checkSelfPermission(_context, Manifest.permission.WRITE_EXTERNAL_STORAGE);

        if (permission != PackageManager.PERMISSION_GRANTED) {
            // We don't have permission so prompt the user
            ActivityCompat.requestPermissions(
                    (AppCompatActivity) _context,
                    PERMISSIONS_STORAGE,
                    REQUEST_EXTERNAL_STORAGE
            );
        } else {
            // TODO Auto-generated method stub
            Intent intent = new Intent();
            intent.setType("image/*");
            intent.setAction(Intent.ACTION_GET_CONTENT);
            startActivityForResult(Intent.createChooser(intent, "Choose File to Upload.."), PICK_FILE_REQUEST);
        }
    }

    public void onActivityResult(int requestCode, int resultCode, Intent intent) {
        // Scanner functionality removed due to missing dependency
        // IntentResult scanningResult = IntentIntegrator.parseActivityResult(requestCode, resultCode, intent);
        if (true) {
            // TODO Auto-generated method stub
            super.onActivityResult(requestCode, resultCode, intent);
            if (resultCode == RESULT_OK) {
                Uri targetUri = intent.getData();

                Bitmap bitmap;
                try {
                    bitmap = BitmapFactory.decodeStream(((AppCompatActivity) _context).getContentResolver().openInputStream(targetUri));
                    //targetImage.setImageBitmap(bitmap);
                    //listImages.add(bitmap);
                    List<Bitmap> temBitmap = new ArrayList<>();
                    temBitmap.add(bitmap);
                    ImageAdapter imageAdapter = new ImageAdapter(_context, temBitmap);
                    productImage = bitmap;
                    gallery.setAdapter(imageAdapter);

                } catch (FileNotFoundException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        }
    }


    private void setSpinnerStation(ArrayList<String> data, List<Integer> indeces) {

        spinnerStation.setItems(data);
        spinnerStation.hasNoneOption(true);
        spinnerStation.setSelectionArray(indeces);
        spinnerStation.setListener(this);
    }

    @Override
    public void selectedIndices(List<Integer> indices) {

        for (int i = 0; i < currentBusinesslocation.size(); i++) {
            if (indices.contains(i) && i != 0) {
                currentBusinesslocation.get(i).setSelected(1);
                //    selectedStations.add(currentStation.get(i));
            } else {
                currentBusinesslocation.get(i).setSelected(0);
                // selectedStations.add(currentStation.get(i));
            }
        }
    }

    @Override
    public void selectedStrings(List<String> strings) {

    }
}