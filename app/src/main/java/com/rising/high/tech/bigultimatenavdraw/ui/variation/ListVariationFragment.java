package com.rising.high.tech.bigultimatenavdraw.ui.variation;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TableLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.VariationTemplateDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationTemplateValuesDbController;
import com.rising.high.tech.bigultimatenavdraw.model.VariationTemplateValues;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_template;
import com.rising.high.tech.bigultimatenavdraw.ui.variation.adapter.ListAddVariationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.variation.adapter.ListVariationAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import java.util.ArrayList;
import java.util.Objects;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.USER_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.VARIATION_ADD;

public class ListVariationFragment extends Fragment {
    private static final String TAG = "ListVariationFragment";
    private Context _context;


    @BindView(R.id.id_filter)
    Button btnFilter;
    @BindView(R.id.noItemFound)
    TextView noItemFound;
    @BindView(R.id.id_search_edit)
    SearchView search_edit;

    ListVariationAdapter variationAdapter;
    VariationTemplateDbController variationTemplateDbController;
    VariationTemplateValuesDbController variationTemplateValuesDbController;
    private ArrayList<Variation_template> dataList = new ArrayList<>();
    RecyclerView recycle_variation;
    Button btnAdd;
    SessionManager session;

    public ListVariationFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_list_variation, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();

        session = new SessionManager(_context);

        recycle_variation = root.findViewById(R.id.recycle_variation);
        btnAdd = root.findViewById(R.id.id_add);

        checkRoles();

        initDB();
        setUpRecyclerView();
        initListners();

        return root;
    }

    private void checkRoles(){
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(VARIATION_ADD))
        {
            btnAdd.setVisibility(View.GONE);
        }
    }

    private void initListners() {
        btnAdd.setOnClickListener(v -> showDetail());
        search_edit.setQueryHint("Search Here");
        search_edit.setOnQueryTextListener(new SearchView.OnQueryTextListener() {

            @Override
            public boolean onQueryTextSubmit(String query) {
                variationAdapter.setData(variationTemplateDbController.getVariationLike(query));
                setItemView();
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                variationAdapter.setData(variationTemplateDbController.getVariationLike(newText));
                setItemView();
                return false;
            }
        });
    }

    private void initDB() {
        variationTemplateDbController = new VariationTemplateDbController(_context);
        variationTemplateDbController.open();

        variationTemplateValuesDbController = new VariationTemplateValuesDbController(_context);
        variationTemplateValuesDbController.open();
    }

    private void setUpRecyclerView() {
        dataList = variationTemplateDbController.getAllXVariation();
        variationAdapter = new ListVariationAdapter();
        variationAdapter.setData(dataList);
        variationAdapter.setonClickAction(new ListVariationAdapter.onClickAction() {
            @Override
            public void onClickEdit(Variation_template variation_template) {
                editDetail(variation_template);
            }

            @Override
            public void onClickDelete(int position) {
                deleteItem(position);
            }
        });
        recycle_variation.setAdapter(variationAdapter);
        recycle_variation.setLayoutManager(new LinearLayoutManager(_context));
        variationAdapter.notifyDataSetChanged();
        setItemView();
    }

    private void deleteItem(int position) {
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(_context);
        alertDialogBuilder.setView(promptsView);

        final Button ButtonDelete = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());

        ButtonDelete.setOnClickListener(v -> {

            variationTemplateDbController.deleteItem(dataList.get(position).getId());
            dataList.remove(position);
            mAlertDialog.dismiss();
            variationAdapter.notifyDataSetChanged();
            setItemView();
        });


        mAlertDialog.show();
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    private void showDetail() {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.variation_add_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                _context);

        alertDialogBuilder.setView(promptsView);
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_add_value);
        final AppCompatEditText imageButtonAdd = promptsView.findViewById(R.id.add_btn);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);
        final TextView btnVariationName = promptsView.findViewById(R.id.btn_variation_name);
        ListAddVariationAdapter listAddVariationAdapter = new ListAddVariationAdapter();
        recyclerView.setAdapter(listAddVariationAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(_context));

        ArrayList<VariationTemplateValues> values = listAddVariationAdapter.getData();
        values.add(new VariationTemplateValues());
        listAddVariationAdapter.setData(values);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        imageButtonAdd.setOnClickListener(v -> {
            ArrayList<VariationTemplateValues> values1 = listAddVariationAdapter.getData();
            values1.add(new VariationTemplateValues());
            listAddVariationAdapter.setData(values1);
        });


        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());

        ButtonSave.setOnClickListener(v -> {
            boolean isEmptyValue = false;
            for (VariationTemplateValues variationTemplate : listAddVariationAdapter.getData()) {
                if (variationTemplate.getName() == null || variationTemplate.getName().isEmpty()) {
                    isEmptyValue = true;
                    break;
                }
            }
            if (btnVariationName.getText().toString().isEmpty()) {
                StringFormat.showSnackBar(promptsView, R.string.variation_name_should_not_be_empty, true);
            } else if (listAddVariationAdapter.getData().size() == 0 || isEmptyValue) {
                StringFormat.showSnackBar(promptsView, R.string.variation_value_should_not_be_empty, true);
            } else {

                Variation_template xvar = new Variation_template(btnVariationName.getText().toString(), 1);
                int insertId = variationTemplateDbController.insertLocal(xvar);
                if (insertId > 0) {
                    for (VariationTemplateValues variationTemplate : listAddVariationAdapter.getData()) {
                        VariationTemplateValues vtv = new VariationTemplateValues(variationTemplate.getName(), insertId);
                        VariationTemplateValuesDbController.insertLocal(vtv);
                    }
                } else {
                    StringFormat.showSnackBar(promptsView, R.string.failed_to_update_data, true);
                }
                mAlertDialog.dismiss();
                FileUtil.showDialog(_context, getString(R.string.success), getResources().getString(R.string.variation_added_success));
                dataList = variationTemplateDbController.getAllXVariation();
                variationAdapter.setData(dataList);
            }

            setItemView();
        });

        mAlertDialog.show();
    }

    public void setItemView() {
        if (variationAdapter.getItemCount() > 0) {
            recycle_variation.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recycle_variation.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }

    private void editDetail(Variation_template variation_template) {
        //Preparing views
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.variation_add_main, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(_context);
        alertDialogBuilder.setView(promptsView);
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_add_value);
        final AppCompatEditText imageButtonAdd = promptsView.findViewById(R.id.add_btn);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);
        final TextView btnVariationName = promptsView.findViewById(R.id.btn_variation_name);

        ArrayList<VariationTemplateValues> tmpVariationTemplate;
        btnVariationName.setText(variation_template.getName());
        tmpVariationTemplate = variationTemplateValuesDbController.getAllVariationValues(variation_template.getId());

        ListAddVariationAdapter listAddVariationAdapter = new ListAddVariationAdapter();
        recyclerView.setAdapter(listAddVariationAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(_context));
        listAddVariationAdapter.setData(tmpVariationTemplate);
        listAddVariationAdapter.notifyDataSetChanged();

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        imageButtonAdd.setOnClickListener(v -> {
            ArrayList<VariationTemplateValues> values1 = listAddVariationAdapter.getData();
            values1.add(new VariationTemplateValues());
            listAddVariationAdapter.setData(values1);
        });


        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());


        ButtonSave.setOnClickListener(v -> {

            if (!btnVariationName.getText().toString().equals("") && listAddVariationAdapter.getData().size() > 0) {
                variation_template.setName(btnVariationName.getText().toString());
                variationTemplateDbController.updateLocal(variation_template);

                if (variation_template.getId() > 0) {
                    VariationTemplateValuesDbController.deleteItem(variation_template.getId());
                    for (VariationTemplateValues variationTemplate : listAddVariationAdapter.getData()) {
                        System.out.println("variationTemplate.getName(), " + variationTemplate.getName());
                        System.out.println("variationTemplate.updateId(), " + variation_template.getId());
                        VariationTemplateValues vtv = new VariationTemplateValues(variationTemplate.getName(), variation_template.getId());
                        VariationTemplateValuesDbController.insertLocal(vtv);
                    }
                } else {
                    Toast.makeText(_context, R.string.failed_to_update_data, Toast.LENGTH_LONG).show();

                }
                mAlertDialog.dismiss();
                FileUtil.showDialog(_context, getString(R.string.success), getResources().getString(R.string.variation_added_success));
                dataList = variationTemplateDbController.getAllXVariation();
                variationAdapter.setData(dataList);
                variationAdapter.notifyDataSetChanged();

            } else {
                Toast.makeText(getContext(), getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
            }
            setItemView();

        });


        mAlertDialog.show();
    }

}