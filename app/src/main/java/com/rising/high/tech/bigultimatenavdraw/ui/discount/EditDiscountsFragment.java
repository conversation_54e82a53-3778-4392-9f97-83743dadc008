package com.rising.high.tech.bigultimatenavdraw.ui.discount;

import android.app.DatePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

// Removed multispinner imports
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BrandDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountDbController;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountVariationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.model.Discount_variation;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinBrandAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinCategoryAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.stocktransfers.StockTransferFragment;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class EditDiscountsFragment extends Fragment implements View.OnClickListener {


    private static final String TAG = "AddDiscountstFragment";
    private Context _context;
    final Calendar c = Calendar.getInstance();

    DiscountDbController discountDbController;
    DiscountVariationDbController discountVariationDbController;
    ProductDbController productDbController;
    BusinessLocationDbController businessLocationDbController;
    CategoryDbController categoryDbController;
    BrandDbController brandDbController;
    SpinStationAdapter spinStationAdapter;
    SpinBrandAdapter spinBrandAdapter;
    SpinCategoryAdapter spinCategoryAdapter;

    private ArrayList<Product> dataListProduct = null;

    @BindView(R.id.multipleItemSelectionSpinner)
    Spinner multiSelectSpinnerWithSearch;
    @BindView(R.id.name_discount)
    EditText nameDiscount;
    @BindView(R.id.spinner_brand)
    Spinner spinnerBrand;
    @BindView(R.id.spinner_category)
    Spinner spinnerCategory;
    @BindView(R.id.spinner_location)
    Spinner spinnerLocation;
    @BindView(R.id.discount_type_spinner)
    Spinner spinnerDiscountType;
    @BindView(R.id.priority)
    EditText priority;
    @BindView(R.id.starts_at)
    EditText startsAt;
    @BindView(R.id.ends_at)
    EditText endsAt;
    @BindView(R.id.discount_amnt)
    EditText discountAmnt;
    @BindView(R.id.add_btn)
    Button addBtn;
    @BindView(R.id.id_back)
    Button backBtn;
    @BindView(R.id.check_spg)
    CheckBox checkSpg;
    @BindView(R.id.is_active)
    CheckBox checkActive;
    @BindView(R.id.title)
    TextView title;

    @BindView(R.id.linearLayout)
    RelativeLayout linearLayout;
    @BindView(R.id.spin_brand_container)
    FrameLayout spinBrandContainer;
    @BindView(R.id.spin_category_container)
    FrameLayout spinCategoryContainer;
    private ArrayList<Integer> productIds = new ArrayList<>();

    private Integer id_discount;
    ArrayList<String> listArray1;
    ArrayList<String> productNames;
    ArrayList<Integer> productIdsList;

    public EditDiscountsFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_add_discount, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        //searchEdit.setOnClickListener(this);

        initDb();
        initSpinners();
        initBtnClicks();
        initSerachEdit();
        Bundle args = getArguments();

        id_discount = args.getInt("id", 0);

        setData();
        initMultiSpinnerSearch();

        return root;
    }

    private void setData() {

        title.setText(getResources().getString(R.string.label_update_disc));
        addBtn.setText(getResources().getString(R.string.label_updatee));

        Discount discount = discountDbController.getDiscountById(id_discount);
        nameDiscount.setText(discount.getName());
        priority.setText(discount.getPriority() + "");
        discountAmnt.setText(discount.getDiscount_amount());
        startsAt.setText(discount.getStarts_at());
        endsAt.setText(discount.getEnds_at());

        if (discount.getIs_active() != 0) checkActive.setChecked(true);
        if (discount.getApplicable_in_spg() != 0) checkSpg.setChecked(true);
        if (discount.getBrand_id() > 0) {
            Brand brand = brandDbController.getBrandById(discount.getBrand_id());
            int spinnerPosition = spinBrandAdapter.getPosition(brand);
            spinnerBrand.setSelection(spinnerPosition);
        }
        if (discount.getCategory_id() > 0) {
            Category category = categoryDbController.getCategoryById(discount.getCategory_id());
            int spinnerPosition = spinCategoryAdapter.getPosition(category);
            spinnerCategory.setSelection(spinnerPosition);
        }
        if (discount.getLocation_id() > 0) {
            Business_location businesslocation = businessLocationDbController.getStationById(discount.getLocation_id());
            int spinnerPosition = spinStationAdapter.getPosition(businesslocation);
            spinnerLocation.setSelection(spinnerPosition);
        }

        spinnerDiscountType.setSelection(discount.getDiscount_type().equals("fixed") ? 1 : 2);

        ArrayList<Discount_variation> discount_variations = discountVariationDbController.getDiscountVarByDiscountId(id_discount);
        Log.d(TAG, "///// discount_variations " + new Gson().toJson(discount_variations));
        Log.d(TAG, "///// id_discount " + id_discount);

        ArrayList<Product> productArrayList = new ArrayList<>();
        for (Discount_variation discount_variation1 : discount_variations) {
            Product product = productDbController.getProductById(discount_variation1.getVariation_id());
            productArrayList.add(product);
        }

        // Create a simple adapter for the spinner
        ArrayList<String> productNames = new ArrayList<>();
        ArrayList<Integer> productIdsList = new ArrayList<>();

        for (Product product : productDbController.getAllProduct()) {
            productNames.add(product.getName());
            productIdsList.add(product.getId());

            // If this product is in the selected products list, add it to productIds
            if (productArrayList.contains(product)) {
                productIds.add(product.getId());
            }
        }

        // Make these variables class members
        this.productNames = productNames;
        this.productIdsList = productIdsList;


        if (productArrayList.size() > 0) {
            spinnerCategory.setEnabled(false);
            spinnerCategory.setClickable(false);
            spinnerCategory.setSelection(0);
            spinCategoryContainer.setBackgroundColor(getResources().getColor(R.color.gray_white));

            spinnerBrand.setEnabled(false);
            spinnerBrand.setClickable(false);
            spinnerBrand.setSelection(0);
            spinBrandContainer.setBackgroundColor(getResources().getColor(R.color.gray_white));

        } else {
            spinnerCategory.setEnabled(true);
            spinnerCategory.setClickable(true);
            spinCategoryContainer.setBackgroundColor(getResources().getColor(R.color.white));

            spinnerBrand.setEnabled(true);
            spinnerBrand.setClickable(true);
            spinBrandContainer.setBackgroundColor(getResources().getColor(R.color.white));

        }
    }

    private void initBtnClicks() {
        startsAt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                startsAt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        endsAt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                endsAt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        backBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListDiscountFragment());
            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addDiscount();
            }
        });
    }

    private void initSpinners() {

        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerLocation.setAdapter(spinStationAdapter);

        spinCategoryAdapter = new SpinCategoryAdapter(_context, R.layout.custom_spinner_item, categoryDbController.getAllCategorySpinner());
        spinnerCategory.setAdapter(spinCategoryAdapter);

        spinBrandAdapter = new SpinBrandAdapter(_context, R.layout.custom_spinner_item, brandDbController.getAllBrandSpinner());
        spinnerBrand.setAdapter(spinBrandAdapter);


    }


    private void initDb() {
        discountDbController = new DiscountDbController(_context);

        discountVariationDbController = new DiscountVariationDbController(_context);

        productDbController = new ProductDbController(_context);

        businessLocationDbController = new BusinessLocationDbController(_context);

        categoryDbController = new CategoryDbController(_context);

        brandDbController = new BrandDbController(_context);

    }

    private void addDiscount() {
        if (nameDiscount.getText().toString().equals("")) {
            //     Toast.makeText(_context, _context.getResources().getString(R.string.label_select_ssupplier), Toast.LENGTH_LONG).show();
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_select_namme), Snackbar.LENGTH_LONG);
            snackbar.show();
        } else if (spinnerLocation.getSelectedItemPosition() == 0) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.lbl_please_select_station), Snackbar.LENGTH_LONG);
            snackbar.show();
        } else if (priority.getText().toString().equals("")) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_plz_spriority), Snackbar.LENGTH_LONG);
            snackbar.show();
        } else if (spinnerDiscountType.getSelectedItemPosition() == 0) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_choose_seldect_discount_type), Snackbar.LENGTH_LONG);
            snackbar.show();
        } else if (discountAmnt.getText().toString().equals("")) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_selectamuuunt), Snackbar.LENGTH_LONG);
            snackbar.show();
        } else if (!startsAt.getText().toString().equals("") && !endsAt.getText().toString().equals("")) {
            Discount discount = new Discount();

            discount.setId(id_discount);
            discount.setName(nameDiscount.getText().toString());
            discount.setPriority(Integer.parseInt(priority.getText().toString()));
            discount.setDiscount_type(spinnerDiscountType.getSelectedItem().toString().toLowerCase());
            discount.setDiscount_amount(discountAmnt.getText().toString());
            discount.setStarts_at(startsAt.getText().toString());
            discount.setEnds_at(endsAt.getText().toString());
            discount.setLocation_id(spinnerLocation.getId());
            discount.setApplicable_in_spg(checkSpg.isChecked() ? 1 : 0);
            discount.setIs_active(checkActive.isChecked() ? 1 : 0);
            discount.setApplicable_in_cg(0);
            Business_location businesslocation = (Business_location) spinnerLocation.getSelectedItem();
            discount.setLocation_id(businesslocation.getId());

            Category category = (Category) spinnerCategory.getSelectedItem();
            discount.setCategory_id(category.getId());

            Brand brand = (Brand) spinnerBrand.getSelectedItem();
            discount.setBrand_id(brand.getId());

            discount.setBusiness_id(1);
            Log.d(TAG, "///// discount " + new Gson().toJson(productIds));

            try {
                int discount_id = discountDbController.update(discount);
                if (discount_id > 0) {
                    discountVariationDbController.deleteByDiscountId(id_discount);
                    if (productIds.size() > 0) {
                        for (int p : productIds) {
                            Discount_variation discount_variation = new Discount_variation();
                            discount_variation.setDiscount_id(id_discount);
                            discount_variation.setVariation_id(p);

                            discountVariationDbController.insertLocal(discount_variation);
                        }
                    }

                    FileUtil.showDialog(_context, "Successful", getResources().getString(R.string.stock_discunt_success));
                    replaceFragment(new ListDiscountFragment());

                    // discountVariationDbController.insertLocal()
                } else {
                    Toast.makeText(_context, getResources().getString(R.string.lbl_error_insert), Toast.LENGTH_LONG).show();
                }

            } catch (Exception e) {
                Log.d(TAG, "error " + e.getMessage());
            }

        } else {
            Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
        }
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    @Override
    public void onClick(View v) {

    }

    private void initSerachEdit() {
        //TODO
        // IMPLEMENT Thread solution

//        dataListProduct = productDbController.getAllProduct();
//
//
//        String[] names = new String[dataListProduct.size()];
//        for (int i = 0; i < dataListProduct.size(); i++) {
//            names[i] = dataListProduct.get(i).getName();
//        }
//
//        final ArrayAdapter<String> adapter = new ArrayAdapter<String>(_context, android.R.layout.simple_list_item_1, names);
//        searchEdit.setAdapter(adapter);
//        searchEdit.setDropDownBackgroundDrawable(new ColorDrawable(_context.getResources().getColor(R.color.blue)));
//
//        searchEdit.setOnItemClickListener(new AdapterView.OnItemClickListener() {
//
//            @Override
//            public void onItemClick(AdapterView<?> parent, View arg1, int pos,
//                                    long id) {
//
//                //  searchProduct(adapter.getItem(pos).toString());
//
//            }
//        });

    }

    private void initMultiSpinnerSearch() {
        /**
         * Simple Spinner setup with product list
         */

        // Create adapter from the product names list
        ArrayAdapter<String> adapter = new ArrayAdapter<>(
            getContext(),
            android.R.layout.simple_spinner_dropdown_item,
            productNames
        );

        multiSelectSpinnerWithSearch.setAdapter(adapter);

        // Set initial selection if there are products already selected
        if (!productIds.isEmpty() && productIdsList.contains(productIds.get(0))) {
            int position = productIdsList.indexOf(productIds.get(0));
            multiSelectSpinnerWithSearch.setSelection(position);
        }

        multiSelectSpinnerWithSearch.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                productIds.clear();
                if (position > 0) {
                    spinnerCategory.setEnabled(false);
                    spinnerCategory.setClickable(false);
                    spinnerCategory.setSelection(0);
                    spinCategoryContainer.setBackgroundColor(getResources().getColor(R.color.gray_white));

                    spinnerBrand.setEnabled(false);
                    spinnerBrand.setClickable(false);
                    spinnerBrand.setSelection(0);
                    spinBrandContainer.setBackgroundColor(getResources().getColor(R.color.gray_white));

                } else {
                    spinnerCategory.setEnabled(true);
                    spinnerCategory.setClickable(true);
                    spinCategoryContainer.setBackgroundColor(getResources().getColor(R.color.white));

                    spinnerBrand.setEnabled(true);
                    spinnerBrand.setClickable(true);
                    spinBrandContainer.setBackgroundColor(getResources().getColor(R.color.white));
                }

                // Add the selected product ID to the list
                if (position >= 0 && position < productIdsList.size()) {
                    productIds.add(productIdsList.get(position));
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // Do nothing
            }
        });
    }


}