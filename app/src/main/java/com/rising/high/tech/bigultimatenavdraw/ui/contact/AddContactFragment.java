package com.rising.high.tech.bigultimatenavdraw.ui.contact;

import android.app.DatePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CustomerGroupsDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Customer_groups;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinCustomerGroupsAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.Functions;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Objects;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ACTIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BOTH;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CUSTOMER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DAYS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DUE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.FINAL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.MONTHS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.OPENING_BALANCE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SUPPLIER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.YEARS;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class AddContactFragment extends Fragment {

    private static final String TAG = "AddContactFragment";
    private Context _context;
    private ContactDbController contactDbController;
    private HashMap<Object, Object> currentHashMap;
    final Calendar c = Calendar.getInstance();
    @BindView(R.id.shipping_adresse)
    EditText shippingAdresse;
    @BindView(R.id.zip_code)
    EditText zip_code;
    @BindView(R.id.country_txt)
    EditText countryTxt;
    @BindView(R.id.state)
    EditText state;
    @BindView(R.id.city_txt)
    EditText cityTxt;
    @BindView(R.id.tax_number)
    EditText tax_number;
    @BindView(R.id.opening_balance)
    EditText opening_balance;
    @BindView(R.id.pay_term_number)
    EditText pay_term_number;
    @BindView(R.id.pay_term_type)
    Spinner pay_term_type;
    @BindView(R.id.credit_limit)
    EditText credit_limit;
    @BindView(R.id.spinner_customer_groups)
    Spinner spinnerCustomerGroups;
    @BindView(R.id.frame_customer_groups)
    FrameLayout frame_customer_groups;
    @BindView(R.id.relativeLayuot)
    RelativeLayout relativeLayuot;
    @BindView(R.id.spinner_prefix)
    Spinner spinnerPrefix;

    private SpinCustomerGroupsAdapter customerGroupsAdapter;
    private CustomerGroupsDbController customerGroupsDbController;
    private TransactionDbController transactionDbController;
    Spinner spinnerType;
    EditText birthDateTxt, adressTxt, codePostalTxt, firstNameTxt, phoneTxt, bussinessNameTxt, lastNameTxt, lineFixeTxt, emailTxt;
    Button addBtn, btnBack;
    SessionManager session;
    private Integer userId;

    public AddContactFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_add_contact, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        session = new SessionManager(_context);

        spinnerType = root.findViewById(R.id.spinner_type);
        bussinessNameTxt = root.findViewById(R.id.bussiness_name_txt);
        firstNameTxt = root.findViewById(R.id.first_name_txt);
        lastNameTxt = root.findViewById(R.id.last_name_txt);
        phoneTxt = root.findViewById(R.id.phone_txt);
        lineFixeTxt = root.findViewById(R.id.line_fixe_txt);
        emailTxt = root.findViewById(R.id.email_txt);
        birthDateTxt = root.findViewById(R.id.birth_date_txt);
        adressTxt = root.findViewById(R.id.adress_txt);
        codePostalTxt = root.findViewById(R.id.code_postal_txt);
        addBtn = root.findViewById(R.id.add_btn);
        btnBack = root.findViewById(R.id.id_back);
        currentHashMap = new HashMap<>();

        initDb();
        initListners();
        initSpinner();

        userId = (int) session.getUserDetails().get(session.ID_USER);

        return root;
    }

    private void initDb() {
        contactDbController = new ContactDbController(_context);
        customerGroupsDbController = new CustomerGroupsDbController(_context);
        transactionDbController = new TransactionDbController(_context);
    }

    private void initSpinner() {
        customerGroupsAdapter = new SpinCustomerGroupsAdapter(_context, android.R.layout.simple_spinner_item, customerGroupsDbController.getAllCustomerGroupsSpin());
        spinnerCustomerGroups.setAdapter(customerGroupsAdapter);
    }

    private void initListners() {
        bussinessNameTxt.setHint(getResources().getString(R.string.image_nom_entrprise_requiered));
        spinnerType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                switch (position) {
                    case 0: {
                        frame_customer_groups.setVisibility(View.VISIBLE);
                        bussinessNameTxt.setHint(getResources().getString(R.string.image_nom_entrprise));
                        break;
                    }
                    case 1: {
                        bussinessNameTxt.setHint(getResources().getString(R.string.image_nom_entrprise));
                        frame_customer_groups.setVisibility(View.VISIBLE);
                        break;
                    }
                    case 2: {
                        bussinessNameTxt.setHint(getResources().getString(R.string.image_nom_entrprise_requiered));
                        frame_customer_groups.setVisibility(View.GONE);
                        break;
                    }
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

//                if ( spinnerType.getSelectedItemPosition() == 0) {
//                    Snackbar snackbar = Snackbar.make(relativeLayuot, _context.getResources().getString(R.string.label_select_type), Snackbar.LENGTH_LONG);
//                    snackbar.show();
//                }else
                if (!Functions.isValidEmail(emailTxt.getText().toString())) {
                    Snackbar snackbar = Snackbar.make(relativeLayuot, _context.getResources().getString(R.string.enter_valide_email), Snackbar.LENGTH_LONG);
                    snackbar.show();
                } else if (spinnerType.getSelectedItemPosition() == 2 || spinnerType.getSelectedItemPosition() == 0) {
                    if (!bussinessNameTxt.getText().toString().equals("")) {
                        postContact();
                    } else {
                        Snackbar snackbar = Snackbar.make(relativeLayuot, _context.getResources().getString(R.string.enter_valide_bbusines_name), Snackbar.LENGTH_LONG);
                        snackbar.show();
                    }
                } else if (spinnerType.getSelectedItemPosition() == 1) {
                    postContact();
                }
            }
        });

        birthDateTxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                int age = c.get(Calendar.YEAR) - year;

                                if (age < 16) {
                                    Toast.makeText(getActivity(), getResources().getString(R.string.string_age_must_be_over), Toast.LENGTH_LONG).show();
                                } else {
                                    String myFormat = populateSetDate(year, month, day);
                                    currentHashMap.put("dob", myFormat);
                                    birthDateTxt.setText(myFormat);
                                }

                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        btnBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListContactFragment());
            }
        });

    }


    private void postContact() {


        if (phoneTxt.getText().toString().length()==0){
            Snackbar snackbar = Snackbar.make(relativeLayuot, _context.getResources().getString(R.string.label_enter_phome_number_name), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (!firstNameTxt.getText().toString().equals("")) {

            Contact contact = new Contact();
            contact.setBusiness_id(1);
            contact.setFirst_name(firstNameTxt.getText().toString());
            contact.setLast_name(lastNameTxt.getText().toString());
            contact.setMobile(phoneTxt.getText().toString());
            contact.setDob(birthDateTxt.getText().toString());
            contact.setAddress_line_1(adressTxt.getText().toString());
            contact.setEmail(emailTxt.getText().toString());
            contact.setCountry(countryTxt.getText().toString());
            contact.setCity(cityTxt.getText().toString());
            contact.setContact_status(ACTIVE);
            contact.setSync(NO);
            contact.setName(firstNameTxt.getText().toString() + " " + lastNameTxt.getText().toString());
            contact.setShipping_address(shippingAdresse.getText().toString());
            contact.setZip_code(zip_code.getText().toString());
            contact.setState(state.getText().toString());
            contact.setTax_number(tax_number.getText().toString());
            contact.setPay_term_number(pay_term_number.getText().toString().equals("") ? 0 : Integer.parseInt(pay_term_number.getText().toString()));

            if (pay_term_type.getSelectedItemPosition()==1){
                contact.setPay_term_type(DAYS);
            }else if (pay_term_type.getSelectedItemPosition()==2){
                contact.setPay_term_type(MONTHS);
            }else if (pay_term_type.getSelectedItemPosition()==3){
                contact.setPay_term_type(YEARS);
            }

            contact.setCredit_limit(credit_limit.getText().toString());


            String dateNow = StringFormat.populateSetFullDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
            contact.setCreated_at(dateNow);

            Customer_groups customer_groups = (Customer_groups) spinnerCustomerGroups.getSelectedItem();
            contact.setCustomer_group_id(customer_groups.getId());
            contact.setSupplier_business_name(bussinessNameTxt.getText().toString());
            contact.setCreated_by(userId + "");
            //     contact.setBalance(opening_balance.getText().toString().equals("") ? "00" : opening_balance.getText().toString());

            contact.setPrefix(spinnerPrefix.getSelectedItemPosition()+"");

            contact.setType(spinnerType.getSelectedItemPosition() == 0 ?BOTH :spinnerType.getSelectedItemPosition() == 1 ? CUSTOMER : SUPPLIER);
            int inserted = contactDbController.insertLocal(contact);

//            int inserted = 0;
//            if (spinnerType.getSelectedItemPosition() == 0) {
//                contact.setType(CUSTOMER);
//                inserted = contactDbController.insertLocal(contact);
//                contact.setType(SUPPLIER);
//                inserted = contactDbController.insertLocal(contact);
//
//            } else if (spinnerType.getSelectedItemPosition() == 1) {
//                contact.setType(CUSTOMER);
//                inserted = contactDbController.insertLocal(contact);
//            } else if (spinnerType.getSelectedItemPosition() == 2) {
//                contact.setType(SUPPLIER);
//                inserted = contactDbController.insertLocal(contact);
//            }

            if (inserted > 0) {
                //insert opening balance to transaction table
                if (!opening_balance.getText().toString().equals("")) {
                    Transaction transactionBalance = new Transaction();
                    transactionBalance.setBusiness_id(1);
                    transactionBalance.setLocation_id(1);
                    transactionBalance.setType(OPENING_BALANCE);
                    transactionBalance.setStatus(FINAL);
                    transactionBalance.setIs_quotation(0);
                    transactionBalance.setPayment_status(DUE);
                    transactionBalance.setContact_id(inserted);
                    transactionBalance.setRef_no(StringFormat.generateRefPaymentNo(_context));
                    transactionBalance.setTransaction_date(dateNow);
                    transactionBalance.setTotal_before_tax(opening_balance.getText().toString());
                    transactionBalance.setFinal_total(opening_balance.getText().toString());
                    transactionBalance.setCreated_by(userId);

                    transactionDbController.insertLocal(transactionBalance);
                }
                FileUtil.showDialog(_context, getString(R.string.success), getResources().getString(R.string.contacts_added_success));
                replaceFragment(new ListContactFragment());
            } else Toast.makeText(_context, "Error insert ", Toast.LENGTH_LONG).show();


        } else {
            Snackbar.make(relativeLayuot, _context.getResources().getString(R.string.label_all_field_required), Snackbar.LENGTH_LONG).show();

        }

    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


}