package com.rising.high.tech.bigultimatenavdraw.ui.usermanagement;

import android.app.DatePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.RoleDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UserDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UserHasRolesDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Role;
import com.rising.high.tech.bigultimatenavdraw.model.User;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinRoleAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.Functions;
import com.rising.high.tech.bigultimatenavdraw.util.MultiSelectSpinner;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class EditUserFragment extends Fragment implements MultiSelectSpinner.OnMultipleItemsSelectedListener{
    private static final String TAG = "AddUserFragment";

    private Context _context;
    final Calendar c = Calendar.getInstance();
    private BusinessLocationDbController businessLocationDbController;
    private UserDbController userDbController;

    @BindView(R.id.spinner_station)
    MultiSelectSpinner spinnerStation;
    @BindView(R.id.birth_date_txt)
    EditText birthDateTxt;
    @BindView(R.id.prefix_edittext)
    AppCompatEditText prefixEdittext;
    @BindView(R.id.first_name_edittext)
    AppCompatEditText firstnameEdittext;
    @BindView(R.id.last_name)
    AppCompatEditText lastName;
    @BindView(R.id.username_edittext)
    AppCompatEditText usernameEdittext;
    @BindView(R.id.password_edittext)
    AppCompatEditText passwordEdittext;
    @BindView(R.id.password_confirm_edittext)
    AppCompatEditText passwordConfirmEdittext;
    @BindView(R.id.is_active_checkbox)
    CheckBox isactiveCheckbox;
    @BindView(R.id.spinner_role)
    AppCompatSpinner spinnerRole;
    @BindView(R.id.spinner_gender)
    AppCompatSpinner spinnerGender;
    @BindView(R.id.spinner_marital_status)
    AppCompatSpinner spinnerMaritalStatus;
    @BindView(R.id.email_edittext)
    AppCompatEditText emailEdittext;
    @BindView(R.id.blood_group)
    AppCompatEditText bloodgroupEditText;
    @BindView(R.id.contact_number)
    AppCompatEditText contactnumberEditText;
    @BindView(R.id.social_media)
    AppCompatEditText socialmediaEditText;
    @BindView(R.id.permanante_adress)
    AppCompatEditText permananteadressEditText;
    @BindView(R.id.current_adress)
    AppCompatEditText currentadressEditText;
    @BindView(R.id.add_btn)
    Button addBtn;
    @BindView(R.id.id_back)
    Button btnBack;

    private ArrayList<Business_location> currentBusinesslocation = null;
    private SpinRoleAdapter spinRoleAdapter;
    private RoleDbController roleDbController;
    private UserHasRolesDbController userHasRolesDbController;
    private int indexId = 0;


    public EditUserFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_add_usert, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        Bundle args = getArguments();
        assert args != null;
        indexId = args.getInt("id", 0);
        addBtn.setText(getResources().getString(R.string.label_updatee));

//        spinnerType = root.findViewById(R.id.spinner_type);
//        bussinessNameTxt = root.findViewById(R.id.bussiness_name_txt);
//        firstNameTxt = root.findViewById(R.id.first_name_txt);
//        lastNameTxt = root.findViewById(R.id.last_name_txt);
//        phoneTxt = root.findViewById(R.id.phone_txt);
//        lineFixeTxt = root.findViewById(R.id.line_fixe_txt);
//        emailTxt = root.findViewById(R.id.email_txt);
//        birthDateTxt = root.findViewById(R.id.birth_date_txt);
//        adressTxt = root.findViewById(R.id.adress_txt);
//        codePostalTxt = root.findViewById(R.id.code_postal_txt);
//        addBtn = root.findViewById(R.id.add_btn);
//        btnBack = root.findViewById(R.id.id_back);
//        currentHashMap = new HashMap<>();
//        contactDbController = new ContactDbController(_context);
//        contactDbController.open();
//        customerGroupsDbController = new CustomerGroupsDbController(_context);
//        customerGroupsDbController.open();
//
//        spinnerType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
//            @Override
//            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
//                switch (position) {
//                    case 1: {
//                        bussinessNameTxt.setHint(getResources().getString(R.string.image_nom_entrprise));
//                        frame_customer_groups.setVisibility(View.VISIBLE);
//                        break;
//                    }
//                    case 2: {
//                        bussinessNameTxt.setHint(getResources().getString(R.string.image_nom_entrprise_requiered));
//                        frame_customer_groups.setVisibility(View.GONE);
//                        break;
//                    }
//                }
//            }
//
//            @Override
//            public void onNothingSelected(AdapterView<?> parent) {
//
//            }
//        });
//

//
//        birthDateTxt.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
//                        new DatePickerDialog.OnDateSetListener() {
//                            @Override
//                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
//                                String myFormat = populateSetDate(year, month, day);
//                                currentHashMap.put("dob", myFormat);
//                                birthDateTxt.setText(myFormat);
//                            }
//                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
//                datePickerDialog.show();
//            }
//        });
//
//        btnBack.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                replaceFragment(new ListContactFragment());
//            }
//        });
//        customerGroupsAdapter = new SpinCustomerGroupsAdapter(_context, android.R.layout.simple_spinner_item, customerGroupsDbController.getAllCustomerGroupsSpin());
//        spinnerCustomerGroups.setAdapter(customerGroupsAdapter);


        initDB();
        currentBusinesslocation = businessLocationDbController.getAllStationSpinner();
        ArrayList<String> dataStation = new ArrayList<>();

        if (currentBusinesslocation.size() > 0) {
            for (Business_location businesslocation : currentBusinesslocation) {
                dataStation.add(businesslocation.getName());
            }
            setSpinnerStation(dataStation);
        }

        spinRoleAdapter = new SpinRoleAdapter(_context, android.R.layout.simple_spinner_item, roleDbController.getAllRoles());
        spinnerRole.setAdapter(spinRoleAdapter);

        initListners();
        initForm();
        return root;
    }
    private void initForm()
    {
        User user=userDbController.getUsersById(indexId);
        prefixEdittext.setText(user.getSurname());
        firstnameEdittext.setText(user.getFirst_name());
        lastName.setText(user.getLast_name());
        emailEdittext.setText(user.getEmail());
        usernameEdittext.setText(user.getUsername());
        passwordEdittext.setText(user.getPassword());
        passwordConfirmEdittext.setText(user.getPassword());

        Role role=userHasRolesDbController.getRolesByUserId(indexId);
        Role role1=roleDbController.getRoleById(role.getRole_id());
        int spinnerPosition = spinRoleAdapter.getPosition(role1);
        spinnerRole.setSelection(spinnerPosition);
        birthDateTxt.setText(user.getDob());
        contactnumberEditText.setText(user.getContact_number());
        isactiveCheckbox.setChecked(user.getStatus().matches("true") ? true : false);
    }


    private void initDB()
    {
        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();

        userDbController = new UserDbController(_context);
        userDbController.open();

        roleDbController=new RoleDbController(_context);
        roleDbController.open();

        userHasRolesDbController=new UserHasRolesDbController(_context);
        userHasRolesDbController.open();

    }
    private void initListners()
    {
        birthDateTxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                //currentHashMap.put("dob", myFormat);
                                birthDateTxt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if ( !firstnameEdittext.getText().toString().matches("")
                && !emailEdittext.getText().toString().matches("")
                && !usernameEdittext.getText().toString().matches("")
                && !passwordEdittext.getText().toString().matches("")
                && !passwordConfirmEdittext.getText().toString().matches("")
                && passwordEdittext.getText().toString().matches(passwordConfirmEdittext.getText().toString())) {
                    postUser();
                } else {
                    Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
                }
            }
        });

        btnBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new UsersFragment());
            }
        });

    }

    private void postUser() {

        if (Functions.isValidEmail(emailEdittext.getText().toString()))
        {
            User user=userDbController.getUsersById(indexId);

            user.setSurname(prefixEdittext.getText().toString());
            user.setFirst_name(firstnameEdittext.getText().toString());
            user.setLast_name(lastName.getText().toString());
            user.setEmail(emailEdittext.getText().toString());
            user.setStatus(isactiveCheckbox.isChecked()+"");
            user.setUsername(usernameEdittext.getText().toString());
            user.setPassword(passwordEdittext.getText().toString());
            user.setDob(birthDateTxt.getText().toString());
            if (spinnerGender.getSelectedItemPosition()!=0)  user.setGender(spinnerGender.getSelectedItem().toString());
            if (spinnerMaritalStatus.getSelectedItemPosition()!=0)  user.setMarital_status(spinnerMaritalStatus.getSelectedItem().toString());
            user.setBlood_group(bloodgroupEditText.getText().toString());
            user.setContact_number(contactnumberEditText.getText().toString());
            user.setAddress(currentadressEditText.getText().toString());
            /*
            TODO
             */
            user.setBusiness_id(1);
            user.setSync("no");

            int insertedId=userDbController.editUser(user);
            if(insertedId>0)
            {
                Role user_has_role=new Role();
                user_has_role.setUser_id(indexId);
                Role role=(Role)spinnerRole.getSelectedItem();
                user_has_role.setRole_id(role.getId());
                int idHasInsert= userHasRolesDbController.editUserHasRole(user_has_role);
                if (idHasInsert>0)
                {
                    Toast.makeText(getContext(), getContext().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                    replaceFragment(new UsersFragment());
                }
                else
                {
                    Toast.makeText(getContext(), "Error insert", Toast.LENGTH_LONG).show();

                }

            }
            else
            {
                Toast.makeText(getContext(), getContext().getString(R.string.lbl_error_insert), Toast.LENGTH_LONG).show();
            }
        }else
        {
            Toast.makeText(getContext(), "Non valid email", Toast.LENGTH_LONG).show();

        }


    }


    private void setSpinnerStation(ArrayList<String> data) {

        spinnerStation.setItems(data);
        spinnerStation.hasNoneOption(true);
        // spinnerStation.setSelection(new int[]{0});
        spinnerStation.setListener(this);
      /*
        ArrayAdapter<String> spinnerArrayAdapter = new ArrayAdapter<String>(
                this, R.layout.simple_spinner_items, data);
        spinnerStation.setAdapter(spinnerArrayAdapter);*/
    }

    @Override
    public void selectedIndices(List<Integer> indices) {
        // selectedStations.clear();

        for (int i = 0; i < currentBusinesslocation.size(); i++) {
            if (indices.contains(i) && i != 0) {
                currentBusinesslocation.get(i).setSelected(1);
                //    selectedStations.add(currentStation.get(i));
            } else {
                currentBusinesslocation.get(i).setSelected(0);
                // selectedStations.add(currentStation.get(i));
            }
        }

    }

    @Override
    public void selectedStrings(List<String> strings) {

    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


}