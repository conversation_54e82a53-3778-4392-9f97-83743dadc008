package com.rising.high.tech.bigultimatenavdraw.ui.businessetting;

import android.app.Activity;
import android.app.DatePickerDialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.CompanyDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business;
import com.rising.high.tech.bigultimatenavdraw.model.CurrencyList;
import com.rising.high.tech.bigultimatenavdraw.ui.NaviMainActivity;
import com.rising.high.tech.bigultimatenavdraw.ui.home.HomeFragment;
import com.rising.high.tech.bigultimatenavdraw.util.Constant;
import com.rising.high.tech.bigultimatenavdraw.util.FilePath;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
// Removed SearchableSpinner import

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.TimeZone;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class BusinessSettingsFragment extends Fragment implements AdapterView.OnItemSelectedListener {

    private static final String TAG = "BusinessSettingsFragment";
    private Context _context;
    private CompanyDbController companyDbController;

    @BindView(R.id.businessName)
    AppCompatEditText businessName;
    @BindView(R.id.startDate)
    AppCompatEditText startDate;
    @BindView(R.id.default_percent_profit)
    AppCompatEditText default_percent_profit;
    @BindView(R.id.spinnerCurrency)
    AppCompatSpinner spinnerCurrency;
    @BindView(R.id.currencySymbolPlacement)
    AppCompatSpinner currencySymbolPlacement;
    @BindView(R.id.timeZoneSpinner)
    AppCompatSpinner timeZoneSpinner;
    @BindView(R.id.financialYearSpinner)
    AppCompatSpinner financialYearSpinner;
    @BindView(R.id.spinnerStockAccounting)
    AppCompatSpinner spinnerStockAccounting;
    @BindView(R.id.transactionEditDays)
    AppCompatEditText transactionEditDays;
    @BindView(R.id.dateFormat)
    AppCompatSpinner dateFormat;
    @BindView(R.id.timeFormat)
    AppCompatSpinner timeFormat;
    @BindView(R.id.uploadLogo)
    AppCompatTextView uploadLogo;
    @BindView(R.id.idBack)
    AppCompatButton idBack;
    @BindView(R.id.addBtn)
    AppCompatButton addBtn;
    @BindView(R.id.logoImage)
    ImageView logoImage;
    SessionManager session;

    private static final int PICK_FILE_REQUEST = 1;
    String currencyId="2",selectedLogoPath="";
    ArrayList<String> currencyListString;
    ArrayList<String> timeZoneList;
    ArrayList<CurrencyList> currencyListsArray;
    Boolean isEdit=false;
    int businessId;
    public BusinessSettingsFragment() {
        // Required empty public constructor
    }
    final Calendar c = Calendar.getInstance();

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View root = inflater.inflate(R.layout.activity_business_setting, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        session = new SessionManager(_context);

        initDB();
        initClickListeners();
        getCurrencyData();
        getTimeZoneList();
        setData();

        if (session.getBoolean(SERVER_MASTER) )
        {
            addBtn.setVisibility(View.GONE);
        }
        return root;
    }

    private void initDB() {
        companyDbController = new CompanyDbController(_context);
        companyDbController.open();
    }

    private void initClickListeners() {
        idBack.setOnClickListener(v ->  replaceFragment(new HomeFragment()));
        startDate.setOnClickListener(v -> {
            pickDate();
        });
        uploadLogo.setOnClickListener(v->
        {
            showFileChooser();
        });

        addBtn.setOnClickListener(v -> {
            if (Objects.requireNonNull(businessName.getText()).toString().isEmpty()) {
                businessName.setError(getString(R.string.enter_business_name));
                businessName.setFocusable(true);
            } else if (Objects.requireNonNull(default_percent_profit.getText()).toString().isEmpty()) {
                default_percent_profit.setError(getString(R.string.enter_default_per));
                default_percent_profit.setFocusable(true);
            } else if (Objects.requireNonNull(transactionEditDays.getText()).toString().isEmpty()) {
                transactionEditDays.setError(getString(R.string.enter_transaction_edit_days));
                transactionEditDays.setFocusable(true);
            }   else {

                    Business business = new Business();
                    business.setName(businessName.getText().toString());
                    business.setStart_date(Objects.requireNonNull(startDate.getText()).toString());
                    business.setDefault_profit_percent(default_percent_profit.getText().toString());
                    business.setCurrency_id(Integer.parseInt(currencyId));
                    business.setCurrency_symbol_placement(currencySymbolPlacement.getSelectedItem().toString());
                    business.setTime_zone(timeZoneSpinner.getSelectedItem().toString());
                    business.setFy_start_month(financialYearSpinner.getSelectedItem().toString());
                    business.setAccounting_method(spinnerStockAccounting.getSelectedItem().toString());
                    business.setTransaction_edit_days(transactionEditDays.getText().toString());
                    business.setDate_format(dateFormat.getSelectedItem().toString());
                    business.setTime_format(timeFormat.getSelectedItem().toString());
                    business.setLogo(selectedLogoPath);
                    business.setSync("no");
                    int idIndex;
                    if(isEdit)
                    {
                         business.setId(businessId);
                         idIndex = companyDbController.update(business);
                    }
                    else {
                         idIndex = companyDbController.insert(business);
                    }
                if (idIndex > 0) {
                    FileUtil.showDialog(_context, "Successful", getResources().getString(R.string.business_settings_success));
                    startActivity(new Intent(_context, NaviMainActivity.class));
                }
            }
        });

    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }
    public void getCurrencyData()
    {
        try {
            // load json from assets
            JSONObject obj = new JSONObject(Objects.requireNonNull(FileUtil.loadJSONFromAsset(_context, Constant.CURRENCY_LIST)));
            currencyListsArray = new ArrayList<>();
            currencyListString = new ArrayList<>();
            JSONArray dataArray = obj.getJSONArray("data");

            for (int i = 0; i < dataArray.length(); i++) {
                obj = dataArray.getJSONObject(i);
                CurrencyList currencyList = new CurrencyList();
                currencyList.setId(obj.optString("id"));
                currencyList.setCountry(obj.optString("country"));
                currencyList.setCurrency(obj.optString("currency"));
                currencyList.setCode(obj.optString("code"));
                currencyList.setSymbol(obj.optString("symbol"));
                currencyListString.add(obj.optString("country") + " - " + obj.optString("currency") +"("+ obj.optString("code") + ")");
                currencyListsArray.add(currencyList);

            }
            spinnerCurrency.setAdapter(new ArrayAdapter<>(_context, android.R.layout.simple_spinner_dropdown_item, currencyListString));
            spinnerCurrency.setOnItemSelectedListener(this);
            spinnerCurrency.setSelection(1);

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
    public void getTimeZoneList()
    {
        String[]TZ = TimeZone.getAvailableIDs();
        timeZoneList = new ArrayList<>();
        for (String s : TZ) {
            if (!(timeZoneList.contains(TimeZone.getTimeZone(s).getDisplayName()))) {
                timeZoneList.add(TimeZone.getTimeZone(s).getID());
            }
        }
        ArrayAdapter<String> adapter= new ArrayAdapter<String>(_context, android.R.layout.simple_spinner_dropdown_item, timeZoneList);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        timeZoneSpinner.setAdapter(adapter);
        timeZoneSpinner.setOnItemSelectedListener(this);
        TimeZone tz = TimeZone.getDefault();
        timeZoneSpinner.setSelection(adapter.getPosition(tz.getID()));

    }


    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        if (parent.getId() == R.id.spinnerCurrency) {
            currencyId=currencyListsArray.get(position).getId();
            session.saveString(session.KEY_SYMBOL, currencyListsArray.get(position).getSymbol());

        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }
    public void pickDate()
    {
        DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                (datePicker, year, month, day) -> {
                    String myFormat = populateSetDate(year, month, day);
                    startDate.setText(myFormat);
                }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
        datePickerDialog.getDatePicker().setMaxDate(new Date().getTime());
        datePickerDialog.show();
    }
    public void setData()
    {
       Business companyData = companyDbController.getCompanyData();
      if(companyData.getName() == null || companyData.getName().isEmpty())
      {
          isEdit=false;
      }
      else {
          isEdit = true;
          addBtn.setText("Update");
          if (session.getBoolean(SERVER_MASTER))
          {
              addBtn.setVisibility(View.GONE);
          }
          businessName.setText(companyData.getName());
          startDate.setText(companyData.getStart_date());
          default_percent_profit.setText(companyData.getDefault_profit_percent());
          transactionEditDays.setText(companyData.getTransaction_edit_days());
          currencyId=String.valueOf(companyData.getCurrency_id());
          businessId=companyData.getId();
          spinnerCurrency.setSelection(companyData.getCurrency_id() - 1);
          if(!(companyData.getLogo() == null || companyData.getLogo().isEmpty()))
          {
              selectedLogoPath=companyData.getLogo();
              logoImage.setImageURI(Uri.parse(companyData.getLogo()));
          }

          String[] currencySymbolList = _context.getResources().getStringArray(R.array.array_currency_symbol_placement);
          ArrayList<String> aList = new ArrayList<String>(Arrays.asList(currencySymbolList));
          aList.addAll(Arrays.asList(currencySymbolList));
          int currencyPlacement = aList.indexOf(companyData.getCurrency_symbol_placement());
          currencySymbolPlacement.setSelection(currencyPlacement);

          int timeZoneSelected = timeZoneList.indexOf(companyData.getTime_zone());
          timeZoneSpinner.setSelection(timeZoneSelected);

          String[] financialList = _context.getResources().getStringArray(R.array.array_year_list);
          ArrayList<String> aFinancialList = new ArrayList<String>(Arrays.asList(financialList));
          aFinancialList.addAll(Arrays.asList(financialList));
          System.out.println("companyData.getFy_start_month()" + companyData.getFy_start_month());
          int financialSelected = aFinancialList.indexOf(companyData.getFy_start_month());
          financialYearSpinner.setSelection(financialSelected);

          String[] accountMethodList = _context.getResources().getStringArray(R.array.array_accounting_method);
          ArrayList<String> aAccountMethodList = new ArrayList<String>(Arrays.asList(accountMethodList));
          aAccountMethodList.addAll(Arrays.asList(accountMethodList));
          int accountingMethodSelected = aAccountMethodList.indexOf(companyData.getAccounting_method());
          spinnerStockAccounting.setSelection(accountingMethodSelected);
          System.out.println("companyData.getAccounting_method()" + companyData.getAccounting_method());


          String[] dateFormatList = _context.getResources().getStringArray(R.array.type_date_format);
          ArrayList<String> aDateFormatList = new ArrayList<String>(Arrays.asList(dateFormatList));
          aDateFormatList.addAll(Arrays.asList(dateFormatList));
          int dateFormatSelected = aDateFormatList.indexOf(companyData.getDate_format());
          dateFormat.setSelection(dateFormatSelected);
          System.out.println("companyData.getDate_format()" + companyData.getDate_format());


          String[] timeFormatList = _context.getResources().getStringArray(R.array.type_time_format);
          ArrayList<String> aTimeFormatList = new ArrayList<String>(Arrays.asList(timeFormatList));
          aTimeFormatList.addAll(Arrays.asList(timeFormatList));
          int timeFormatSelected = aTimeFormatList.indexOf(companyData.getTime_format());
          timeFormat.setSelection(timeFormatSelected);
          System.out.println("companyData.getTime_format()" + companyData.getTime_format());

      }
    }
    private void showFileChooser() {
        Intent intent = new Intent();
        intent.setType("image/*");
        intent.setAction(Intent.ACTION_GET_CONTENT);
        startActivityForResult(Intent.createChooser(intent,"Choose File to Upload.."),PICK_FILE_REQUEST);
    }
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(resultCode == Activity.RESULT_OK){
            if(requestCode == PICK_FILE_REQUEST){
                if(data == null){
                    //no data present
                    return;
                }
                Uri selectedFileUri = data.getData();
                selectedLogoPath = FilePath.getPath(_context,selectedFileUri);
                if(selectedLogoPath != null && !selectedLogoPath.equals("")){
                    logoImage.setImageURI(Uri.parse(selectedLogoPath));
                }else{
                    Toast.makeText(_context,"Failed to pick Image",Toast.LENGTH_SHORT).show();
                }
            }
        }
    }
}