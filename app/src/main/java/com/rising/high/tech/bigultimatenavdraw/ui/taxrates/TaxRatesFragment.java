package com.rising.high.tech.bigultimatenavdraw.ui.taxrates;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.TaxRatesDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.ui.taxrates.adapter.TaxRatesAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.TAX_RATE_ADD;

public class TaxRatesFragment extends Fragment {


    private static final String TAG = "TaxRatesFragment";
    private Context _context;

    SessionManager session;

    Button addBtn;
    RecyclerView recycle_taxe_rates;
    TaxRatesAdapter taxRatesAdapter;
    TaxRatesDbController taxRatesDbController;
    private Integer userId;

    @BindView(R.id.id_filter)
    Button btnFilter;
    @BindView(R.id.id_search_edit)
    SearchView search_edit;
    @BindView(R.id.noItemFound)
    TextView noItemFound;
    public TaxRatesFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_list_tax_rates, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        session = new SessionManager(_context);

        recycle_taxe_rates = root.findViewById(R.id.recycle_taxe_rates);
        addBtn = root.findViewById(R.id.id_add);

        taxRatesDbController = new TaxRatesDbController(_context);
        taxRatesDbController.open();

        taxRatesAdapter = new TaxRatesAdapter();
        recycle_taxe_rates.setAdapter(taxRatesAdapter);
        recycle_taxe_rates.setLayoutManager(new LinearLayoutManager(_context));

        taxRatesAdapter.setData(taxRatesDbController.getAllTax_rates());

        initListners();
        setItemView();

        checkRoles();
        userId=(int)session.getUserDetails().get(session.ID_USER);

        return root;
    }


    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(TAX_RATE_ADD))
        {
            addBtn.setVisibility(View.INVISIBLE);
        }
    }


    private void initListners()
    {


        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addTaxRates();
            }
        });

        search_edit.setQueryHint("Search Here");
        search_edit.setOnQueryTextListener(new SearchView.OnQueryTextListener() {

            @Override
            public boolean onQueryTextSubmit(String query) {
                taxRatesAdapter.setData(taxRatesDbController.getTaxRatesLike(query));
                setItemView();
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                taxRatesAdapter.setData(taxRatesDbController.getTaxRatesLike(newText));
                setItemView();
                return false;
            }
        });

    }

    public void setItemView() {
        if (taxRatesAdapter.getItemCount() > 0) {
            recycle_taxe_rates.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recycle_taxe_rates.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


    private void addTaxRates() {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.tax_rates_add_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                _context);

        alertDialogBuilder.setView(promptsView);

        final EditText txtName = promptsView.findViewById(R.id.tax_name);
        final EditText tax_amount = promptsView.findViewById(R.id.tax_amount);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);


        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!txtName.getText().toString().equals("") && !tax_amount.getText().toString().equals("")) {

                    Tax_rates tax_rates = new Tax_rates();
                    tax_rates.setName(txtName.getText().toString());
                    tax_rates.setAmount(tax_amount.getText().toString());
                    /*
                    TODO add created by
                     */
                    tax_rates.setCreated_by(userId);
                    tax_rates.setSync(NO);

                    int inserted = taxRatesDbController.insertLocal(tax_rates);
                    if (inserted > 0) {
                        Toast.makeText(getContext(), getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                        taxRatesAdapter.setData(taxRatesDbController.getAllTax_rates());
                        taxRatesAdapter.notifyDataSetChanged();
                        setItemView();
                        mAlertDialog.dismiss();
                    } else {
                        Toast.makeText(getContext(), "Error while adding", Toast.LENGTH_LONG).show();
                    }
                } else {
                    Toast.makeText(getContext(), getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
                }

            }
        });
        mAlertDialog.show();
    }
}