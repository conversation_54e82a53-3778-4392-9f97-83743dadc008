package com.rising.high.tech.bigultimatenavdraw.ui.contact;

import android.app.DatePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CustomerGroupsDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Customer_groups;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinCustomerGroupsAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.Functions;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ACTIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BOTH;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CUSTOMER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DAYS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DUE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.FINAL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.MONTHS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.OPENING_BALANCE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SUPPLIER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.YEARS;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class EditContactFragment extends Fragment {
    private static final String TAG = "AddContactFragment";
    private Context _context;
    private ContactDbController contactDbController;
    private HashMap<Object, Object> currentHashMap;
    final Calendar c = Calendar.getInstance();

    Spinner spinnerType;
    EditText birthDateTxt, adressTxt, codePostalTxt, firstNameTxt, phoneTxt, bussinessNameTxt, lastNameTxt, lineFixeTxt, emailTxt;
    Button addBtn, btnBack;
    TextView subTitle;
    private Contact currentContact = null;

    private int indexId = 0;
    @BindView(R.id.shipping_adresse)
    EditText shippingAdresse;
    @BindView(R.id.zip_code)
    EditText zip_code;
    @BindView(R.id.country_txt)
    EditText countryTxt;
    @BindView(R.id.state)
    EditText state;
    @BindView(R.id.city_txt)
    EditText cityTxt;
    @BindView(R.id.tax_number)
    EditText tax_number;
    @BindView(R.id.opening_balance)
    EditText opening_balance;
    @BindView(R.id.pay_term_number)
    EditText pay_term_number;
    @BindView(R.id.pay_term_type)
    Spinner pay_term_type;
    @BindView(R.id.credit_limit)
    EditText credit_limit;
    @BindView(R.id.spinner_customer_groups)
    Spinner spinnerCustomerGroups;
    @BindView(R.id.relativeLayuot)
    RelativeLayout relativeLayuot;
    @BindView(R.id.frame_customer_groups)
    FrameLayout frame_customer_groups;
    @BindView(R.id.spinner_prefix)
    Spinner spinnerPrefix;

    private SpinCustomerGroupsAdapter customerGroupsAdapter;
    private CustomerGroupsDbController customerGroupsDbController;
    private TransactionDbController transactionDbController;
    SessionManager session;
    private Integer userId;

    public EditContactFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_add_contact, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        session = new SessionManager(_context);

        Bundle args = getArguments();
        indexId = args.getInt(ID, 0);

        spinnerType = root.findViewById(R.id.spinner_type);
        bussinessNameTxt = root.findViewById(R.id.bussiness_name_txt);
        firstNameTxt = root.findViewById(R.id.first_name_txt);
        lastNameTxt = root.findViewById(R.id.last_name_txt);
        phoneTxt = root.findViewById(R.id.phone_txt);
        lineFixeTxt = root.findViewById(R.id.line_fixe_txt);
        emailTxt = root.findViewById(R.id.email_txt);
        birthDateTxt = root.findViewById(R.id.birth_date_txt);
        adressTxt = root.findViewById(R.id.adress_txt);
        codePostalTxt = root.findViewById(R.id.code_postal_txt);
        addBtn = root.findViewById(R.id.add_btn);
        btnBack = root.findViewById(R.id.id_back);
        subTitle = root.findViewById(R.id.sub_title);
        subTitle.setText(getResources().getString(R.string.label_update_customer));
        addBtn.setText(getResources().getString(R.string.label_updatee));
        currentHashMap = new HashMap<>();
        initDb();
        initListners();
        initSpinners();
        initForm();
        userId = (int) session.getUserDetails().get(session.ID_USER);
        return root;
    }

    private void initDb() {
        contactDbController = new ContactDbController(_context);
        customerGroupsDbController = new CustomerGroupsDbController(_context);
        transactionDbController = new TransactionDbController(_context);
    }

    private void initSpinners() {
        customerGroupsAdapter = new SpinCustomerGroupsAdapter(_context, android.R.layout.simple_spinner_item, customerGroupsDbController.getAllCustomerGroupsSpin());
        spinnerCustomerGroups.setAdapter(customerGroupsAdapter);
    }

    private void initListners() {
        spinnerType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                switch (position) {
                    case 0: {
                        frame_customer_groups.setVisibility(View.VISIBLE);
                        bussinessNameTxt.setHint(getResources().getString(R.string.image_nom_entrprise_requiered));
                        break;
                    }
                    case 1: {
                        bussinessNameTxt.setHint(getResources().getString(R.string.image_nom_entrprise));
                        frame_customer_groups.setVisibility(View.VISIBLE);
                        break;
                    }
                    case 2: {
                        bussinessNameTxt.setHint(getResources().getString(R.string.image_nom_entrprise_requiered));
                        frame_customer_groups.setVisibility(View.GONE);
                        break;
                    }
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!Functions.isValidEmail(emailTxt.getText().toString())) {
                    Snackbar snackbar = Snackbar.make(relativeLayuot, _context.getResources().getString(R.string.enter_valide_email), Snackbar.LENGTH_LONG);
                    snackbar.show();
                } else if (spinnerType.getSelectedItemPosition() == 2 || spinnerType.getSelectedItemPosition() == 0) {
                    if (!bussinessNameTxt.getText().toString().equals("")) {
                        postContact();
                    } else {
                        Snackbar snackbar = Snackbar.make(relativeLayuot, _context.getResources().getString(R.string.enter_valide_bbusines_name), Snackbar.LENGTH_LONG);
                        snackbar.show();
                    }
                } else if (spinnerType.getSelectedItemPosition() == 1) {
                    postContact();
                }
            }
        });

        birthDateTxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                currentHashMap.put("dob", myFormat);
                                birthDateTxt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        btnBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListContactFragment());
            }
        });
    }

    private void initForm() {
        currentContact = contactDbController.getCustomerById(indexId);
        firstNameTxt.setText(currentContact.getFirst_name());
        lastNameTxt.setText(currentContact.getLast_name());
        phoneTxt.setText(currentContact.getMobile());
        birthDateTxt.setText(currentContact.getDob());
        emailTxt.setText(currentContact.getEmail());
        adressTxt.setText(currentContact.getAddress_line_1());
        shippingAdresse.setText(currentContact.getShipping_address());
        zip_code.setText(currentContact.getZip_code());
        countryTxt.setText(currentContact.getCountry());
        state.setText(currentContact.getState());
        cityTxt.setText(currentContact.getCity());
        adressTxt.setText(currentContact.getAddress_line_1());
        tax_number.setText(currentContact.getTax_number());
        pay_term_number.setText(currentContact.getPay_term_number() + "");

        if (currentContact.getPay_term_type()!=null)
        pay_term_type.setSelection(currentContact.getPay_term_type().equals(DAYS) ? 1 : (currentContact.getPay_term_type().equals(MONTHS) ? 2 :(currentContact.getPay_term_type().equals(YEARS) ?  3 : 0)));

        credit_limit.setText(currentContact.getCredit_limit());

        //get index of contact type
        int indexT = 0;
        if (currentContact.getType().matches(CUSTOMER)) {
            indexT = 1;
        } else if (currentContact.getType().matches(SUPPLIER)) {
            bussinessNameTxt.setHint(getResources().getString(R.string.image_nom_entrprise_requiered));
            frame_customer_groups.setVisibility(View.GONE);
            indexT = 2;
        }else if (currentContact.getType().matches(BOTH)){
            bussinessNameTxt.setHint(getResources().getString(R.string.image_nom_entrprise_requiered));
            indexT = 0;
        }
        spinnerType.setSelection(indexT);


        if (currentContact.getCustomer_group_id() > 0) {
            Customer_groups customer_groups = customerGroupsDbController.getCustomerGroupsById(currentContact.getCustomer_group_id());
            int spinnerPosition = customerGroupsAdapter.getPosition(customer_groups);
            spinnerCustomerGroups.setSelection(spinnerPosition);
        }
        bussinessNameTxt.setText(currentContact.getSupplier_business_name());

        if (transactionDbController.getOpeningBalanceContact(indexId) != null)
            opening_balance.setText(transactionDbController.getOpeningBalanceContact(indexId).getFinal_total());

        if (currentContact.getPrefix()!=null && currentContact.getPrefix().length()==1){
            int indexx = Integer.parseInt(currentContact.getPrefix());
            spinnerPrefix.setSelection(indexx);
        }
    }

    private void postContact() {
        if (phoneTxt.getText().toString().length()==0){
            Snackbar snackbar = Snackbar.make(relativeLayuot, _context.getResources().getString(R.string.label_enter_phome_number_name), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (!firstNameTxt.getText().toString().equals("")) {
            Contact contact = contactDbController.getCustomerById(indexId);
            contact.setFirst_name(firstNameTxt.getText().toString());
            contact.setLast_name(lastNameTxt.getText().toString());
            contact.setName(firstNameTxt.getText().toString() + " " + lastNameTxt.getText().toString());
            contact.setMobile(phoneTxt.getText().toString());
            contact.setDob(birthDateTxt.getText().toString());
            contact.setAddress_line_1(adressTxt.getText().toString());
            contact.setEmail(emailTxt.getText().toString());
            contact.setCountry(countryTxt.getText().toString());
            contact.setCity(cityTxt.getText().toString());
            contact.setContact_status(ACTIVE);
            contact.setSync(NO);
            contact.setShipping_address(shippingAdresse.getText().toString());
            contact.setZip_code(zip_code.getText().toString());
            contact.setState(state.getText().toString());
            contact.setTax_number(tax_number.getText().toString());
            contact.setBalance(opening_balance.getText().toString());
            contact.setPay_term_number(pay_term_number.getText().toString().equals("") ? 0 : Integer.parseInt(pay_term_number.getText().toString()));
            if (pay_term_type.getSelectedItemPosition()==1){
                contact.setPay_term_type(DAYS);
            }else if (pay_term_type.getSelectedItemPosition()==2){
                contact.setPay_term_type(MONTHS);
            }else if (pay_term_type.getSelectedItemPosition()==3){
                contact.setPay_term_type(YEARS);
            }else {
                contact.setPay_term_type("");
            }
            contact.setCredit_limit(credit_limit.getText().toString());
            Customer_groups customer_groups = (Customer_groups) spinnerCustomerGroups.getSelectedItem();
            contact.setCustomer_group_id(customer_groups.getId());
            contact.setSupplier_business_name(bussinessNameTxt.getText().toString());
            contact.setCreated_by(userId + "");

            contact.setType(spinnerType.getSelectedItemPosition() == 0 ? BOTH : spinnerType.getSelectedItemPosition() == 1 ? CUSTOMER : SUPPLIER);
            contact.setPrefix(spinnerPrefix.getSelectedItemPosition()+"");

            int inserted = contactDbController.editContact(contact);

//            if (spinnerType.getSelectedItemPosition() == 0) {
//                contact.setType(CUSTOMER);
//                inserted = contactDbController.insertLocal(contact);
//                contact.setType(SUPPLIER);
//                inserted = contactDbController.insertLocal(contact);
//
//            } else if (spinnerType.getSelectedItemPosition() == 1) {
//                contact.setType(CUSTOMER);
//                inserted = contactDbController.insertLocal(contact);
//            } else if (spinnerType.getSelectedItemPosition() == 2) {
//                contact.setType(SUPPLIER);
//                inserted = contactDbController.insertLocal(contact);
//            }
            if (inserted > 0) {
                //insert opening balance to transaction table
                if (!opening_balance.getText().toString().equals("")) {
                    if (transactionDbController.getOpeningBalanceContact(indexId) != null) {
                        Transaction transactionBalance = transactionDbController.getOpeningBalanceContact(indexId);
                        transactionBalance.setFinal_total(opening_balance.getText().toString());
                        transactionDbController.updateTransaction(transactionBalance);
                    } else {
                        String dateNow = StringFormat.populateSetFullDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                        Transaction transactionBalance = new Transaction();
                        transactionBalance.setBusiness_id(1);
                        transactionBalance.setLocation_id(1);
                        transactionBalance.setType(OPENING_BALANCE);
                        transactionBalance.setStatus(FINAL);
                        transactionBalance.setIs_quotation(0);
                        transactionBalance.setPayment_status(DUE);
                        transactionBalance.setContact_id(indexId);
                        transactionBalance.setRef_no(StringFormat.generateRefPaymentNo(_context));
                        transactionBalance.setTransaction_date(dateNow);
                        transactionBalance.setTotal_before_tax(opening_balance.getText().toString());
                        transactionBalance.setFinal_total(opening_balance.getText().toString());
                        transactionBalance.setCreated_by(userId);

                        transactionDbController.insertLocal(transactionBalance);
                    }

                }
                FileUtil.showDialog(_context, getString(R.string.success), getResources().getString(R.string.contacts_updated_success));
                replaceFragment(new ListContactFragment());
            } else Toast.makeText(_context, "Error insert ", Toast.LENGTH_LONG).show();


        } else {
            Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
        }

    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


}