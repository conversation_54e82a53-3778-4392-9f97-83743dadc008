package com.rising.high.tech.bigultimatenavdraw.ui.stockadjustement;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.ui.stockadjustement.adapter.StockAdjustementAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.STOCK_ADJUSTMENT;

public class StockAdjustementFragment extends Fragment {
    private static final String TAG = "StockAdjustementFragment";
    private Context _context;

    private StockAdjustementAdapter stockAdjustementAdapter;
    private TransactionDbController transactionDbController;
    SessionManager session;

    @BindView(R.id.btn_add)
    Button btnAdd;
    @BindView(R.id.recycler_item)
    RecyclerView recyclerItem;
    @BindView(R.id.noItemFound)
    TextView noItemFound;
    @BindView(R.id.id_filter)
    Button btnFilter;
    @BindView(R.id.id_search_edit)
    EditText searchEdit;
    public StockAdjustementFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_stock_adjustement_main, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        session = new SessionManager(_context);

        stockAdjustementAdapter = new StockAdjustementAdapter();
        recyclerItem.setAdapter(stockAdjustementAdapter);
        recyclerItem.setLayoutManager(new LinearLayoutManager(_context));

        initDB();
        initListners();
        checkRoles();

        return root;
    }

    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(PURCHASE_ADD))
        {
            btnAdd.setVisibility(View.INVISIBLE);
        }
    }


    public void setItemView() {
        if (stockAdjustementAdapter.getItemCount() > 0) {
            recyclerItem.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recyclerItem.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }
    private void initDB() {

        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();
    }

    private void initListners() {

        btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new AddStockAdjustementFragment());
            }
        });

        btnFilter.setOnClickListener(v -> {
            Log.d(TAG, "GGGGGG ");
            if (searchEdit.getText().toString().length()!=0){
                stockAdjustementAdapter.setData(transactionDbController.getTransactionLikeType(STOCK_ADJUSTMENT, searchEdit.getText().toString()));
            }else {
                stockAdjustementAdapter.setData(transactionDbController.getTransactionType(STOCK_ADJUSTMENT));
            }
            stockAdjustementAdapter.notifyDataSetChanged();
            setItemView();

        });

        stockAdjustementAdapter.setData(transactionDbController.getTransactionType(STOCK_ADJUSTMENT));
        setItemView();
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

}