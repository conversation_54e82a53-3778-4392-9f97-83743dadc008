package com.rising.high.tech.bigultimatenavdraw.ui.home;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Array;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.AssetManager;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.navigation.NavigationView;
import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
// Removed zxing imports
import com.pax.gl.page.IPage;
import com.pax.gl.page.PaxGLPage;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CustomerGroupsDbController;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountDbController;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountVariationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PayementDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TaxRatesDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Customer_groups;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.model.Payement;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.CategoryAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.ProductAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinContactAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.businessetting.BusinessSettingsFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.businesslocation.BusinessLocationFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.home.adapter.DetailProductAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.home.adapter.MultiPayAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.product.AddProductFragment;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.PrinterTester;
import com.rising.high.tech.bigultimatenavdraw.util.ProductUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;

import butterknife.ButterKnife;
import io.reactivex.rxjava3.annotations.NonNull;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ACTIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CASH;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DRAFT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.EXCLUSIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.FINAL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.FIXED;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.INCLUSIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PERCENTAGE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SELL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;

public class HomeFragment extends Fragment {

    private static final String TAG = "HomeFragment";

    private static final int MIN_FRAME_WIDTH = 10;  // (your desired value here)
    private static final int MIN_FRAME_HEIGHT = 50; // (your desired value here)

    private HomeViewModel homeViewModel;
    private Context _context;
    private SpinStationAdapter spinStationAdapter;
    private SpinContactAdapter spinContactAdapter;
    private BusinessLocationDbController businessLocationDbController;
    private ContactDbController contactDbController;
    private VariationsDbController variationsDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private Integer currentIdLocation = 0;
    private Integer currentSelectedContact = 0;

    private ArrayList<Product> currentProducts = new ArrayList<>();
    private Payement initPayement;
    private ArrayList<Payement> tmpDataPayement;
    int result;

    LinearLayout left_view, right_view, left_view_multi_pay, productCategoryLayout, addProductBtn;
    RelativeLayout no_report_layout;
    RecyclerView detailRecycler;
    Spinner spinner_station, spinner_customers;
    GridView categoryGrid, productGrid;
    DetailProductAdapter detailProductAdapter;
    CategoryAdapter categoryAdapter;
    ProductAdapter productAdapter;
    CategoryDbController categoryDbController;
    ProductDbController productDbController;
    TransactionSellLineDbController transactionSellLineDbController;
    DiscountDbController discountDbController;
    DiscountVariationDbController discountVariationDbController;
    TaxRatesDbController taxRatesDbController;
    TextView total_price, customer_name;
    EditText searchEdit;
    LinearLayout header_toolbar;
    ImageView btnScan;
    SwipeRefreshLayout fragment_category_swipe_refresh;
  //  ImageView scroll_img;
    LinearLayout id_cancel, linearLayout;
    LinearLayout id_pay_cash, id_save_btn, id_multi_pay;
    Handler handler = new Handler();
    DrawerLayout drawer_layout;
    NavigationView drawer_nav_view;
    Resources res;

    // Session Manager Class
    SessionManager session;
    HashMap<String, Object> user;
    private ArrayList<Business_location> businesslocationList;

    private TransactionDbController transactionDbController;
    private PayementDbController payementDbController;
    private TransactionPayementDbController transactionPayementDbController;
    private CustomerGroupsDbController customerGroupsDbController;

    Button id_cancel_multipay, add_payement_multipay, btnMultiPayement;
    MultiPayAdapter mutliPayAdapter;
    RecyclerView recycleMulti;
    TextView totalItems, totalPayable, totalPaying, returnChange;
    String totalPrice = "00.0";
    String totalItem = "0";
    final Calendar c = Calendar.getInstance();

    public PaxGLPage iPaxGLPage;
    private Bitmap bitmap;

    private static final int FONT_BIG = 28;
    private static final int FONT_NORMAL = 20;
    private static final int FONT_BIGEST = 40;
    String discountAmount = "0.0", discountType = "";
    private PurchaseLineDbController purchaseLineDbController;

    public View onCreateView(@NonNull LayoutInflater inflater,
                             ViewGroup container, Bundle savedInstanceState) {
        homeViewModel = new ViewModelProvider(this).get(HomeViewModel.class);
        getActivity().getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        View root = inflater.inflate(R.layout.fragment_home, container, false);
        ButterKnife.bind(this, root);

        _context = getActivity();
        res = getResources();

        left_view = root.findViewById(R.id.layout_left_side);
        right_view = root.findViewById(R.id.layout_right_side);
        linearLayout = root.findViewById(R.id.linearLayout);
        left_view_multi_pay = root.findViewById(R.id.layout_left_side_multi_pay);
        detailRecycler = left_view.findViewById(R.id.list_item_product);
        //     spinner_station = left_view.findViewById(R.id.spinner_station);
        spinner_customers = left_view.findViewById(R.id.spinner_customers);
        total_price = left_view.findViewById(R.id.total_price);
        categoryGrid = right_view.findViewById(R.id.id_grid_category);
        productCategoryLayout = right_view.findViewById(R.id.productCategoryLayout);
        no_report_layout = right_view.findViewById(R.id.no_report_layout);
        addProductBtn = right_view.findViewById(R.id.addProductBtn);
        fragment_category_swipe_refresh = right_view.findViewById(R.id.fragment_category_swipe_refresh);
    //    scroll_img = right_view.findViewById(R.id.scroll_img);
        productGrid = right_view.findViewById(R.id.id_grid_product);
        btnScan = right_view.findViewById(R.id.btn_scan);
        searchEdit = right_view.findViewById(R.id.id_search_edit);
        id_pay_cash = left_view.findViewById(R.id.id_pay_cash);
        id_save_btn = left_view.findViewById(R.id.id_save_btn);
        customer_name = left_view.findViewById(R.id.customer_name);
        id_cancel = left_view.findViewById(R.id.id_cancel_btn);
        id_multi_pay = left_view.findViewById(R.id.id_multi_pay);
        id_cancel_multipay = left_view_multi_pay.findViewById(R.id.id_cancel_btn);
        add_payement_multipay = left_view_multi_pay.findViewById(R.id.id_add_payement);
        recycleMulti = left_view_multi_pay.findViewById(R.id.id_recycle);
        drawer_layout = root.findViewById(R.id.drawer_layout);
        header_toolbar = root.findViewById(R.id.header_toolbar);
        totalItems = left_view_multi_pay.findViewById(R.id.id_total_items);
        totalPayable = left_view_multi_pay.findViewById(R.id.id_total_payable);
        totalPaying = left_view_multi_pay.findViewById(R.id.id_total_paying);
        returnChange = left_view_multi_pay.findViewById(R.id.id_return_change);
        btnMultiPayement = left_view_multi_pay.findViewById(R.id.id_btn_multi_payement);

        detailProductAdapter = new DetailProductAdapter();
        detailRecycler.setAdapter(detailProductAdapter);
        detailRecycler.setLayoutManager(new LinearLayoutManager(_context));
        categoryAdapter = new CategoryAdapter(_context);
        categoryGrid.setAdapter(categoryAdapter);
        productAdapter = new ProductAdapter(_context);
        productGrid.setAdapter(productAdapter);
        mutliPayAdapter = new MultiPayAdapter();
        recycleMulti.setAdapter(mutliPayAdapter);
        recycleMulti.setLayoutManager(new LinearLayoutManager(_context));
        tmpDataPayement = new ArrayList<>();

        session = new SessionManager(_context);
        user = session.getUserDetails();
        fragment_category_swipe_refresh.setEnabled(false);
        fragment_category_swipe_refresh.setRefreshing(false);

        initDB();
        initspinners();
        initListners();


        Bundle args = getArguments();
        if (args != null) {
            Integer indexId = args.getInt(ID, 0);
            ArrayList<Product> productArrayList = transactionSellLineDbController.getSellLineProductsByTransaction(indexId);
            updateDetailList(productArrayList);
        }

        return root;
    }

    @Override
    public void onStart() {
        super.onStart();

    }

    private void initspinners() {
        businesslocationList = new ArrayList<>();
        businesslocationList = businessLocationDbController.getAllStationSpinner();

        spinContactAdapter = new SpinContactAdapter(_context, R.layout.custom_spinner_item, contactDbController.getSpinnerActiveCustomer());
        spinner_customers.setAdapter(spinContactAdapter);
    }

    private void initDB() {

        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();

        categoryDbController = new CategoryDbController(_context);

        productDbController = new ProductDbController(_context);

        transactionDbController = new TransactionDbController(_context);

        payementDbController = new PayementDbController(_context);

        transactionSellLineDbController = new TransactionSellLineDbController(_context);

        transactionPayementDbController = new TransactionPayementDbController(_context);

        discountDbController = new DiscountDbController(_context);

        discountVariationDbController = new DiscountVariationDbController(_context);

        taxRatesDbController=new TaxRatesDbController(_context);

        variationLocationDetailDbController = new VariationLocationDetailDbController(_context);

        contactDbController = new ContactDbController(_context);

        variationsDbController = new VariationsDbController(_context);


        purchaseLineDbController = new PurchaseLineDbController(_context);
        customerGroupsDbController = new CustomerGroupsDbController(_context);

    }

    private void initListners() {
      //  scroll_img.setVisibility(categoryDbController.getAllCategorySpinner().size() > 4 ? View.VISIBLE : View.GONE);
        categoryAdapter.setDataCategoryAdapter(categoryDbController.getAllCategorySpinner());
        productAdapter.setProductAdapter(productDbController.getHomeProduct());
        categoryAdapter.setOnDataChangeListener(new CategoryAdapter.OnDataChangeListener() {
            public void onDataChanged(Category category) {
                if (category.getId() == 0) {
                    productAdapter.setProductAdapter(productDbController.getHomeProduct());
                } else {
                    productAdapter.setProductAdapter(productDbController.getProductByCategory(category.getId()));
                }
                productAdapter.notifyDataSetChanged();
            }
        });

        productAdapter.setOnDataChangeListener(new ProductAdapter.OnDataChangeListener() {
            public void onDataChanged(Product product) {

                if (product != null && currentProducts != null) {
                    boolean isListed = false;
                    for (Product product1 : currentProducts) {
                        if (product1 != null && product1.getId() == product.getId()) {
                            isListed = true;
                        }
                    }
                    if (!isListed) {
                        ArrayList<Discount> discounts = discountDbController.getDiscountByCategory(product.getCategory_id());
                        Variation variation = variationsDbController.getVariationByProductId(product.getId());

                        Float currentSellPrice=0.f;
                        if (product.getTax_type()!=null){
                            if (product.getTax_type().matches(EXCLUSIVE)){
                                currentSellPrice = Float.parseFloat(variation.getDefault_sell_price());
                            }else if (product.getTax_type().matches(INCLUSIVE)){
                                currentSellPrice = Float.parseFloat(variation.getSell_price_inc_tax());
                            }  else {
                                currentSellPrice = Float.parseFloat(variation.getDefault_sell_price());
                            }
                        }else {
                            currentSellPrice = Float.parseFloat(variation.getDefault_sell_price());
                        }

                        product.setDefault_sell_price(currentSellPrice+"");

                     //   product.setDefault_sell_price(variation.getDefault_sell_price());
                        product.setSell_qte(1);
                        ArrayList<Integer> arrIds = discountVariationDbController.getDiscountIds(product.getId());
                        for (Integer id : arrIds) {
                            String currentDate =  StringFormat.populateSetDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                            Discount discountTmp = discountDbController.getDiscountById(id);
                            try{

                                SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");

                                String str1 = discountTmp.getStarts_at();
                                String str2 = discountTmp.getEnds_at();
                                Date dateStart = formatter.parse(str1);
                                Date dateEnd = formatter.parse(str2);

                                Date dateCurrent = formatter.parse(currentDate);

                                if (dateStart.compareTo(dateCurrent)<0 &&dateCurrent.compareTo(dateEnd)<0  )
                                {
                                    discounts.add(discountDbController.getDiscountById(id));
                                //    System.out.println("date2 is Greater than my date1");
                                }

                            }catch (ParseException e1){
                                e1.printStackTrace();
                            }
                        }
                        if (discounts.size() > 0) {
                            ArrayList<Discount> higherDiscounts = ProductUtil.getHigherPriority(_context, discounts);
                            if (higherDiscounts.size() > 0) {
                                Discount higherDiscount = higherDiscounts.get(higherDiscounts.size() - 1);
                                if (higherDiscount.getDiscount_type().equals(FIXED)) {
                                    product.setDefault_sell_price((Float.parseFloat(product.getDefault_sell_price()) - Float.parseFloat(higherDiscount.getDiscount_amount())) + "");
                                }
                                if (higherDiscount.getDiscount_type().equals(PERCENTAGE)) {
                                    product.setDefault_sell_price((Float.parseFloat(product.getDefault_sell_price()) * ((1 - Float.parseFloat(higherDiscount.getDiscount_amount()) / 100))) + "");
                                }
                                discountAmount = higherDiscount.getDiscount_amount();
                                discountType = higherDiscount.getDiscount_type();
                            }
                        }
                        Contact contact = contactDbController.getCustomerById(currentSelectedContact);
                        if (contact.getCustomer_group_id()!=null&&contact.getCustomer_group_id() > 0) {
                            Customer_groups customer_groups = customerGroupsDbController.getCustomerGroupsById(contact.getCustomer_group_id());
                            if (customer_groups.getId()>0){
                                Float customerGroupPrice = Float.parseFloat(customer_groups.getAmount());
                                Float sellPrice = Float.parseFloat(product.getDefault_sell_price());
                                Float priceDefaultSelling = (sellPrice * customerGroupPrice / 100) + sellPrice;
                                product.setDefault_sell_price(priceDefaultSelling + "");
                            }
                        }
                        currentProducts.add(product);
                        updateDetailList(currentProducts);
                    }
                }
            }
        });
        if (productAdapter.getCount() > 0) {
            productCategoryLayout.setVisibility(View.VISIBLE);
            no_report_layout.setVisibility(View.GONE);
        } else {
            no_report_layout.setVisibility(View.VISIBLE);
            productCategoryLayout.setVisibility(View.GONE);
        }

        if (session.getBoolean(SERVER_MASTER)) addProductBtn.setVisibility(View.GONE);

        detailProductAdapter.setOnDataChangeListener(new DetailProductAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(ArrayList<Product> products) {
                Log.d(TAG, " /// clicked here ");
                updateDetailList(products);
            }

            @Override
            public void onDataDeleted(ArrayList<Product> products) {
                currentProducts = products;
                updateDetailList(currentProducts);
            }
        });

        addProductBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (session.getBusinessModel().getId() == 0) {
                    showDialog(_context, res.getString(R.string.label_settup_busines_setting), res.getString(R.string.label_settup_busines_setting_product), new BusinessSettingsFragment());
                } else if (businesslocationList.size() == 1) {
                    showDialog(_context, res.getString(R.string.label_settup_busines_location), res.getString(R.string.label_settup_busines_location_plz_add), new BusinessLocationFragment());
                } else {
                    replaceFragment(new AddProductFragment());
                }

            }
        });

        id_pay_cash.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                try {
                    payCash(false);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        });

        btnMultiPayement.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                checkMultiPay();
            }
        });

        id_save_btn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (currentSelectedContact > 0) {
                    saveItemsTransaction();
                 //   Toast.makeText(_context, "Saved", Toast.LENGTH_LONG).show();

                } else {
                    Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.string_select_customer), Snackbar.LENGTH_LONG);
                    snackbar.show();
                }
            }
        });

        id_cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (currentProducts != null && currentProducts.size() > 0) {
                    currentProducts.clear();
                    productAdapter.setProductAdapter(productDbController.getAllProduct());
                    productAdapter.notifyDataSetChanged();
                    tmpDataPayement.clear();
                    updateDetailList(new ArrayList<>());

                }

            }
        });

        id_cancel_multipay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                left_view.setVisibility(View.VISIBLE);
                left_view_multi_pay.setVisibility(View.GONE);
                currentProducts.clear();
                productAdapter.setProductAdapter(productDbController.getAllProduct());
                productAdapter.notifyDataSetChanged();
                tmpDataPayement.clear();
                updateDetailList(currentProducts);
            }
        });

        id_multi_pay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (currentSelectedContact == 0) {
                    Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.string_select_customer), Snackbar.LENGTH_LONG);
                    snackbar.show();
                }else if (detailProductAdapter.getDataList().size() > 0) {
                    left_view.setVisibility(View.GONE);
                    left_view_multi_pay.setVisibility(View.VISIBLE);
                    addMultiPayement();
                } else {
                    Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_nothing), Snackbar.LENGTH_LONG);
                    snackbar.show();
                }
            }
        });

        add_payement_multipay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addMultiPayement();
            }
        });

        searchEdit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() != 0) {
                    ArrayList<Product> arrayList = productDbController.getProductByName(s.toString());
                    productAdapter.setProductAdapter(arrayList);
                    productAdapter.notifyDataSetChanged();
                } else {
                    productAdapter.setProductAdapter(productDbController.getHomeProduct());
                    productAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        btnScan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startScanCode();
            }
        });

        spinner_customers.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                session.getUserDetails();
                if (position > 0) {
                    Contact contact = (Contact) spinner_customers.getSelectedItem();
                    session.saveInt(session.SELECTED_CONTACT, contact.getId());
                    currentSelectedContact = contact.getId();
                } else {
                    currentSelectedContact = 0;
                    session.saveInt(session.SELECTED_CONTACT, 0);

                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });

        if ((int) user.get(session.LOCATION_ID) > 0) {
            Business_location businesslocation = businessLocationDbController.getStationById((int) user.get(session.LOCATION_ID));
            currentIdLocation = businesslocation.getId();
        }

        if ((int) user.get(session.SELECTED_CONTACT) > 0) {

            Contact contact = contactDbController.getCustomerById((int) user.get(session.SELECTED_CONTACT));
            if (contact.getContact_status().matches(ACTIVE)) {
                spinner_customers.setSelection(spinContactAdapter.getPosition(contact));
                currentSelectedContact = contact.getId();
            } else {
                spinner_customers.setSelection(0);
                currentSelectedContact = 0;
            }

        }

    }

    private void startScanCode() {
        // Scanner functionality removed due to missing dependency
        Toast.makeText(_context, "Scanner functionality is disabled", Toast.LENGTH_SHORT).show();
    }

    private void checkMultiPay() {

        Float totalPriceTransaction = Float.parseFloat(totalPrice);
        Float totalCards = 0.f;
        for (Payement payement : mutliPayAdapter.getDataListPayement()) {
            totalCards += Float.parseFloat(payement.getAmount());
        }
        if (totalCards < totalPriceTransaction) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_amount_payable_innfer), Snackbar.LENGTH_LONG);
            snackbar.show();
        } else if (totalCards > totalPriceTransaction) {
            totalPaying.setText(totalCards + " " + user.get(session.KEY_SYMBOL).toString());
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_amount_payable_moore), Snackbar.LENGTH_LONG);
            snackbar.show();
        } else {
            try {
                payCash(true);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    private void updateDetailList(ArrayList<Product> products) {

        detailProductAdapter.setData(products);
        detailProductAdapter.notifyDataSetChanged();
        Float totalCPrice = 0.f;
        for (Product xProduct : products) {
            totalCPrice += Float.parseFloat(xProduct.getDefault_sell_price()) * xProduct.getSell_qte();
        }

        totalPrice = totalCPrice.toString();
        total_price.setText(String.format("%.2f", totalCPrice) + user.get(SessionManager.KEY_SYMBOL));

    }

    private void addMultiPayement() {
        float tmpTrns = 0;
        for (Payement trs : mutliPayAdapter.getDataListPayement()) {
            tmpTrns = tmpTrns + Float.parseFloat(trs.getAmount());
        }
        if (tmpTrns >= Float.parseFloat(totalPrice)) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_insert_lower_price), Snackbar.LENGTH_LONG);
            snackbar.show();
        } else {
            // calculate this variable if amount is higher than the value
            float flt = Float.parseFloat(totalPrice) - tmpTrns;
            tmpDataPayement.add(new Payement("cash", flt + ""));
            mutliPayAdapter.setDataAdapter(tmpDataPayement);
            mutliPayAdapter.notifyDataSetChanged();

            totalItems.setText(detailProductAdapter.getItemCount() + "");
            totalPayable.setText(totalPrice + user.get(session.KEY_SYMBOL).toString());
            totalPaying.setText(totalPrice + user.get(session.KEY_SYMBOL).toString());
            returnChange.setText("00" + user.get(session.KEY_SYMBOL).toString());
        }
    }


    private Transaction getCurrentTrasaction(String status, String note) {
        Transaction transaction = new Transaction();
        transaction.setBusiness_location(currentIdLocation + "");
        transaction.setBusiness_id(1);
        transaction.setCreated_by((int) user.get(session.ID_USER));
        transaction.setLocation_id(currentIdLocation);
        transaction.setFinal_total(totalPrice);
        transaction.setIs_sync(NO);
        transaction.setStatus(status);
        transaction.setSell_price_tax("");
        transaction.setShipping("");
        transaction.setTax_calculation_amount("");
        transaction.setDiscount_amount(discountAmount);
        transaction.setDiscount_type(discountType);
        transaction.setAmount_return("");
        transaction.setPayment_status(PAID);
        transaction.setType(SELL);
        transaction.setRef_no(StringFormat.generateInvoiceTransactionNo(_context));
        Date currentTime = Calendar.getInstance().getTime();
        String date = StringFormat.populateSetFullDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
        transaction.setTransaction_date(date);
        transaction.setPaid_on(StringFormat.populateSetFullDate(currentTime.getYear(), currentTime.getMonth(), currentTime.getDay(), currentTime.getHours(), currentTime.getMinutes()));
        transaction.setNote(note);
        transaction.setContact_id(currentSelectedContact);
        return transaction;
    }

    public void payCash(boolean isMultipay) throws IOException {

        Object object = new Gson().toJson(PrinterTester.getInstance(_context));

        user = session.getUserDetails();
        if ((int) user.get(session.LOCATION_ID) == 0) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.lbl_please_select_station), Snackbar.LENGTH_LONG);
            snackbar.show();

        } else if (currentSelectedContact == 0) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.string_select_customer), Snackbar.LENGTH_LONG);
            snackbar.show();
        } else if (detailProductAdapter.getDataList().size() > 0) {
            currentIdLocation = (int) user.get(session.LOCATION_ID);

            // insert transaction into transaction table and get id insert

            int insertedId = transactionDbController.insertLocal(getCurrentTrasaction(FINAL, null));

            /**
             * insert payment into transaction_payment table
             */
            if (isMultipay) {
                for (Payement payement : mutliPayAdapter.getDataListPayement()) {
                    // totalCards+=Float.parseFloat(payement.getAmount());
                    Transaction transactionPayement = getCurrentTrasaction(FINAL, null);
                    transactionPayement.setAmount(Float.parseFloat(payement.getAmount()));
                    transactionPayement.setTransaction_id(insertedId);
                    transactionPayement.setMethod(payement.getMethod().toLowerCase());
                    transactionPayement.setPayment_for(currentSelectedContact);
                    transactionPayementDbController.insertLocal(transactionPayement);
                }
            } else {
                Transaction transactionPayement = getCurrentTrasaction(FINAL, null);
                transactionPayement.setAmount(Float.parseFloat(totalPrice));
                transactionPayement.setTransaction_id(insertedId);
                transactionPayement.setMethod(CASH);
                transactionPayement.setPayment_for(currentSelectedContact);
                transactionPayementDbController.insertLocal(transactionPayement);
            }

            /**
             * insert to transaction sell_lines products
             */
            for (Product product : detailProductAdapter.getDataList()) {
                Sell_lines sell_lines = new Sell_lines();
                sell_lines.setTransaction_id(insertedId);
                sell_lines.setProduct_id(product.getId());
                sell_lines.setVariation_id(product.getId());
                sell_lines.setQuantity(product.getSell_qte());
                sell_lines.setUnit_price(product.getDefault_sell_price());
                sell_lines.setUnit_price_inc_tax(product.getSell_price_inc_tax());
                transactionSellLineDbController.insertLocal(sell_lines);

                // update quantity in variation location detail for product quantity
                variationLocationDetailDbController.updateSellQty(product.getId(), currentIdLocation, product.getSell_qte());

            }

            FileUtil.showDialog(_context, getString(R.string.success), getResources().getString(R.string.label_transaction_done));

            productAdapter.notifyDataSetChanged();
            left_view.setVisibility(View.VISIBLE);
            left_view_multi_pay.setVisibility(View.GONE);
//            total_price.setText("00.0" + user.get(SessionManager.KEY_SYMBOL));

            printReceipt();


        } else {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_nothing), Snackbar.LENGTH_LONG);
            snackbar.show();
        }
    }

    private void startPrintTicket(Bitmap bitmap) {
        new Thread(new Runnable() {
            public void run() {
                try {

                    PrinterTester.getInstance(_context).init();
                    PrinterTester.printBitmap(bitmap);
                    PrinterTester.getInstance(_context).spaceSet(Byte.parseByte("0"),
                            Byte.parseByte("0"));
                    PrinterTester.getInstance(_context).leftIndents(Short.parseShort("0"));
                    PrinterTester.getInstance(_context).setGray(Integer.parseInt("1"));
                    PrinterTester.getInstance(_context).setInvert(false);
//                    PrinterTester.getInstance(_context).step(Integer.parseInt("150"));
                    PrinterTester.getInstance(_context).start();
                    PrinterTester.getInstance(_context).cutPaper(0);
                } catch (Exception e) {
                }
            }
        }).start();
    }


    private void saveItemsTransaction() {
        if (detailProductAdapter.getDataList().size() > 0) {
            EditText edittext = new EditText(_context);
            edittext.setTextColor(Color.BLACK);
            edittext.setMaxWidth(20);
            edittext.setHint("Note");
            new AlertDialog.Builder(_context)
                    .setIcon(R.drawable.ic_baseline_save_24)
                    .setTitle(res.getString(R.string.label_save))
                    .setMessage(res.getString(R.string.label_save_transaction))
                    .setView(edittext)
                    .setPositiveButton(res.getString(R.string.label_save), new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            int insertedId = transactionDbController.insertLocal(getCurrentTrasaction(DRAFT, edittext.getText().toString()));

                            for (Product product : detailProductAdapter.getDataList()) {
                                Sell_lines sell_lines = new Sell_lines();
                                sell_lines.setProduct_id(product.getId());
                                sell_lines.setQuantity(product.getSell_qte());
                                sell_lines.setUnit_price(product.getDefault_sell_price());
                                sell_lines.setTransaction_id(insertedId);
                                sell_lines.setUnit_price_inc_tax(product.getSell_price_inc_tax());
                                transactionSellLineDbController.insertLocal(sell_lines);
                            }
                            currentProducts = null;
                            detailProductAdapter.setData(new ArrayList<>());
                            detailProductAdapter.notifyDataSetChanged();
                            Toast.makeText(_context, res.getString(R.string.label_saved), Toast.LENGTH_SHORT).show();
                        }
                    })
                    .setNegativeButton(res.getString(R.string.label_close), null)
                    .show();
        } else {

            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_nothing), Snackbar.LENGTH_LONG);
            snackbar.show();
        }
    }

    public void onActivityResult(int requestCode, int resultCode, Intent intent) {
        super.onActivityResult(requestCode, resultCode, intent);

        // Scanner functionality removed due to missing dependency
        Log.d(TAG, "Scanner functionality is disabled");
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    public void printReceipt() throws IOException {

        iPaxGLPage = PaxGLPage.getInstance(requireContext());
        IPage page = iPaxGLPage.createPage();
        Business_location businesslocation = businessLocationDbController.getStationById(currentIdLocation);
        page.addLine().addUnit(getImageFromAssetsFile("logo_print.png"), IPage.EAlign.CENTER);
        page.addLine().addUnit(businesslocation.getName(), FONT_BIGEST, IPage.EAlign.CENTER, IPage.ILine.IUnit.TEXT_STYLE_BOLD);
        page.addLine().addUnit(businesslocation.getLandmark(), FONT_NORMAL, IPage.EAlign.CENTER, IPage.ILine.IUnit.TEXT_STYLE_BOLD);
        page.addLine().addUnit(businesslocation.getCity() + " - " + businesslocation.getZip_code(), FONT_NORMAL, IPage.EAlign.CENTER, IPage.ILine.IUnit.TEXT_STYLE_BOLD);
        page.addLine().addUnit(businesslocation.getState(), FONT_NORMAL, IPage.EAlign.CENTER, IPage.ILine.IUnit.TEXT_STYLE_BOLD);
        page.addLine().addUnit("Bill No: TRANS-" + transactionDbController.getLastTransactionID(), FONT_NORMAL, IPage.EAlign.LEFT).addUnit("Date & Time: " + StringFormat.getCurrentDateTime(), FONT_NORMAL, IPage.EAlign.RIGHT);
        page.addLine().addUnit("Walk in Customer", FONT_NORMAL, IPage.EAlign.LEFT);
        page.addLine().addUnit("Address:  Not Available \n \n ", FONT_NORMAL, IPage.EAlign.LEFT);
        page.addLine().addUnit("Product", FONT_NORMAL, IPage.EAlign.LEFT).addUnit("Price", FONT_NORMAL, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_BOLD).addUnit("Qty", FONT_NORMAL, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_BOLD).addUnit("Total", FONT_NORMAL, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_BOLD);
        page.addLine().addUnit("------------------------------------------------------------------------------------------------------------", FONT_NORMAL);
        for (Product product : detailProductAdapter.getDataList()) {
            Log.d(TAG, "//// tax products " + new Gson().toJson(product));
            System.out.println();
            Float totalAmount = Float.parseFloat(product.getDefault_sell_price()) * product.getSell_qte();
            page.addLine().addUnit(product.getName(), FONT_NORMAL, IPage.EAlign.LEFT).addUnit(String.valueOf(product.getDefault_sell_price()), FONT_NORMAL, IPage.EAlign.RIGHT).addUnit(String.valueOf(product.getSell_qte()), FONT_NORMAL, IPage.EAlign.RIGHT).addUnit(String.format("%.2f", totalAmount)+" "+ user.get(SessionManager.KEY_SYMBOL), FONT_NORMAL, IPage.EAlign.RIGHT);
        }
        page.addLine().addUnit("------------------------------------------------------------------------------------------------------------", FONT_NORMAL);
        page.addLine().addUnit("Total", FONT_NORMAL, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_BOLD).addUnit(totalPrice, FONT_NORMAL, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_NORMAL);

        page.addLine().addUnit("Service Tax", FONT_NORMAL, IPage.EAlign.RIGHT).addUnit(getTotalTax(detailProductAdapter.getDataList())+ " " + user.get(SessionManager.KEY_SYMBOL), FONT_NORMAL, IPage.EAlign.RIGHT);
        page.addLine().addUnit("--------------------", FONT_NORMAL, IPage.EAlign.RIGHT);
        page.addLine().addUnit("Grand Total", FONT_BIG, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_BOLD).addUnit(totalPrice + " " + user.get(SessionManager.KEY_SYMBOL), FONT_BIG, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_BOLD);
        page.addLine().addUnit("--------------------", FONT_NORMAL, IPage.EAlign.RIGHT);
        page.addLine().addUnit("\n\n", FONT_NORMAL, IPage.EAlign.RIGHT);
        page.addLine().addUnit("Thank You. Visit Again", FONT_BIG, IPage.EAlign.CENTER, IPage.ILine.IUnit.TEXT_STYLE_BOLD);

        int width = 600;
        Bitmap bitmap = iPaxGLPage.pageToBitmap(page, width);

        setBitmap(bitmap);
        startPrintTicket(bitmap);

        detailProductAdapter.setData(new ArrayList<Product>());
        detailProductAdapter.notifyDataSetChanged();
        currentProducts.clear();
    }

    public Float getTotalTax(ArrayList<Product> products){
        Float total =0.f;
        for (Product product: products){
            if (product.getTax()!=null && product.getTax()>0){
         //       Tax_rates tax_rates=taxRatesDbController.getTax_ratesById(product.getTax());
                Variation variation= variationsDbController.getVariationByProductId(product.getId());
                int quantity = product.getSell_qte();
                Float dsp = Float.parseFloat(variation.getDefault_sell_price());
                Float dspInc =Float.parseFloat(variation.getSell_price_inc_tax());
                Float taxDiff =  dspInc-dsp;

                if (product.getTax_type().matches(INCLUSIVE)){
                    total+= (taxDiff*quantity);
                }else {
                    total+=0;
                }
            }
        }

        return total;

    }
    public Bitmap getImageFromAssetsFile(String fileName) {
        Bitmap image = null;
        AssetManager am = requireContext().getResources().getAssets();
        try {
            InputStream is = am.open(fileName);
            image = BitmapFactory.decodeStream(is);
            is.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return image;
    }

    public void setBitmap(Bitmap bitmap) {
        this.bitmap = bitmap;
    }

    public Bitmap getBitmap() {
        return this.bitmap;
    }

    public void showDialog(Context context, String title, String msg, Fragment fragment) {
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.dialog_confirm_layout_home, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(context);
        alertDialogBuilder.setView(promptsView);
        final AppCompatTextView titleTxt = promptsView.findViewById(R.id.title);
        final AppCompatTextView messageTxt = promptsView.findViewById(R.id.message);
        final AppCompatButton noBtn = promptsView.findViewById(R.id.noBtn);
        final AppCompatButton yesBtn = promptsView.findViewById(R.id.yesBtn);
        titleTxt.setText(title);
        messageTxt.setText(msg);
        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        noBtn.setOnClickListener(v -> mAlertDialog.dismiss());

        yesBtn.setOnClickListener(v ->
        {
            mAlertDialog.dismiss();
            replaceFragment(fragment);
        });
        mAlertDialog.show();
        mAlertDialog.getWindow().setLayout(600, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

}